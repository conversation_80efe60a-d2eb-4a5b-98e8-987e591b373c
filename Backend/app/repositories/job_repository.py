from datetime import datetime

from botocore.exceptions import ClientError

from ..logging_config import get_logger
from .base_repository import BaseRepository

logger = get_logger(__name__)


class JobRepository(BaseRepository):
    """Repository for job-related DynamoDB operations"""

    def __init__(self):
        super().__init__()
        self.table = self.aws.jobs_table

    # Create operations
    def create_job(
        self,
        job_id: str,
        file_key: str,
        user_email: str,
        parameters: dict | None = None,
        batch_id: str | None = None,
    ) -> dict:
        """Create a new job record in DynamoDB"""
        timestamp = datetime.utcnow().isoformat()

        item = {
            "job_id": job_id,
            "file_key": file_key,
            "user_email": user_email,
            "status": "pending",
            "created_at": timestamp,
            "updated_at": timestamp,
            "parameters": parameters or {},
        }

        if batch_id:
            item["batch_id"] = batch_id

        if self.table:
            self.table.put_item(Item=item)

        return item

    # Read operations
    def get_job_by_id(self, job_id: str) -> dict | None:
        """Get a single job by its ID"""
        if not self.table:
            return None

        try:
            response = self.table.get_item(Key={"job_id": job_id})
            return response.get("Item")
        except ClientError as e:
            logger.error(f"Error getting job {job_id}: {e}")
            return None

    def get_jobs_by_user(self, user_email: str) -> list[dict]:
        """Get all jobs for a specific user"""
        if not self.table:
            return []

        try:
            response = self.table.query(
                IndexName="UserEmailIndex",
                KeyConditionExpression="user_email = :email",
                ExpressionAttributeValues={":email": user_email},
                ScanIndexForward=False,
            )

            jobs = response.get("Items", [])

            # Handle pagination
            while "LastEvaluatedKey" in response:
                response = self.table.query(
                    IndexName="UserEmailIndex",
                    KeyConditionExpression="user_email = :email",
                    ExpressionAttributeValues={":email": user_email},
                    ScanIndexForward=False,
                    ExclusiveStartKey=response["LastEvaluatedKey"],
                )
                jobs.extend(response.get("Items", []))

            return jobs
        except Exception as e:
            logger.error(f"Error querying jobs for user {user_email}: {e}")
            return []

    def get_jobs_by_batch(self, batch_id: str) -> list[dict]:
        """Get all jobs in a batch"""
        if not self.table:
            return []

        try:
            response = self.table.query(
                IndexName="BatchJobIndex",
                KeyConditionExpression="batch_id = :batch_id",
                ExpressionAttributeValues={":batch_id": batch_id},
            )
            return response.get("Items", [])
        except Exception as e:
            logger.error(f"Error querying batch jobs {batch_id}: {e}")
            return []

    # Update operations
    def update_job_status(self, job_id: str, status: str, **kwargs) -> bool:
        """Update job status and optional fields"""
        if not self.table:
            return False

        try:
            update_expr = "SET #status = :status, updated_at = :updated"
            expr_values = {
                ":status": status,
                ":updated": datetime.utcnow().isoformat(),
            }

            # Add optional fields
            if "error_message" in kwargs:
                update_expr += ", error_message = :error"
                expr_values[":error"] = kwargs["error_message"]

            if "hfo_count" in kwargs:
                update_expr += ", hfo_count = :count"
                expr_values[":count"] = kwargs["hfo_count"]

            if "results_url" in kwargs:
                update_expr += ", results_url = :url"
                expr_values[":url"] = kwargs["results_url"]

            if status == "completed":
                update_expr += ", completed_at = :completed"
                expr_values[":completed"] = datetime.utcnow().isoformat()

            self.table.update_item(
                Key={"job_id": job_id},
                UpdateExpression=update_expr,
                ExpressionAttributeNames={"#status": "status"},
                ExpressionAttributeValues=expr_values,
            )

            return True
        except Exception as e:
            logger.error(f"Error updating job {job_id}: {e}")
            return False
