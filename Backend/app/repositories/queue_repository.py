import json

from ..logging_config import get_logger
from .base_repository import BaseRepository

logger = get_logger(__name__)


class QueueRepository(BaseRepository):
    """Repository for SQS queue operations"""

    MAX_BATCH_SIZE = 10  # SQS limit

    def __init__(self):
        super().__init__()
        self.queue_url = self.aws.sqs_queue_url
        self.sqs = self.aws.sqs

    # Send operations
    def send_message(self, message: dict) -> bool:
        """Send a single message to the queue"""
        if not self.queue_url:
            logger.warning("SQS queue URL not configured")
            return False

        try:
            self.sqs.send_message(
                QueueUrl=self.queue_url,
                MessageBody=json.dumps(message),
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return False

    def send_batch(self, entries: list[dict]) -> tuple[int, int]:
        """Send batch of messages to queue"""
        if not self.queue_url or not entries:
            return 0, len(entries)

        successful = 0
        failed = 0

        # Process in chunks
        for i in range(0, len(entries), self.MAX_BATCH_SIZE):
            batch = entries[i : i + self.MAX_BATCH_SIZE]

            try:
                response = self.sqs.send_message_batch(
                    QueueUrl=self.queue_url, Entries=batch
                )

                successful += len(response.get("Successful", []))
                failed += len(response.get("Failed", []))

                # Log failures
                for failure in response.get("Failed", []):
                    logger.error(
                        f"Failed to send message {failure['Id']}: {failure.get('Message', 'Unknown')}"
                    )

            except Exception as e:
                logger.error(f"Batch send failed: {e}")
                failed += len(batch)

        return successful, failed

    # Queue monitoring
    def get_queue_attributes(self) -> dict | None:
        """Get queue statistics"""
        if not self.queue_url:
            return None

        try:
            response = self.sqs.get_queue_attributes(
                QueueUrl=self.queue_url,
                AttributeNames=[
                    "ApproximateNumberOfMessages",
                    "ApproximateNumberOfMessagesNotVisible",
                    "ApproximateNumberOfMessagesDelayed",
                ],
            )
            return response.get("Attributes", {})
        except Exception as e:
            logger.error(f"Failed to get queue attributes: {e}")
            return None
