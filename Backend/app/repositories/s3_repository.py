import json

from botocore.exceptions import Client<PERSON>rror

from ..logging_config import get_logger
from .base_repository import BaseRepository

logger = get_logger(__name__)


class S3Repository(BaseRepository):
    """Repository for S3 operations"""

    def __init__(self):
        super().__init__()
        self.bucket_name = self.aws.s3_bucket_name
        self.s3 = self.aws.s3

    # File validation
    def file_exists(self, file_key: str) -> bool:
        """Check if a file exists in S3"""
        try:
            self.s3.head_object(Bucket=self.bucket_name, Key=file_key)
            return True
        except ClientError:
            return False

    # Results operations
    def get_results(self, job_id: str) -> dict | None:
        """Get analysis results from S3"""
        results_key = f"results/{job_id}/analysis_results.json"

        try:
            response = self.s3.get_object(Bucket=self.bucket_name, Key=results_key)
            return json.loads(response["Body"].read())
        except ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchKey":
                logger.warning(f"Results not found for job {job_id}")
            else:
                logger.error(f"Error retrieving results for job {job_id}: {e}")
            return None

    def results_exist(self, job_id: str) -> bool:
        """Check if results exist for a job"""
        results_key = f"results/{job_id}/analysis_results.json"
        try:
            self.s3.head_object(Bucket=self.bucket_name, Key=results_key)
            return True
        except ClientError:
            return False

    # Presigned URL generation
    def generate_presigned_url(
        self, key: str, operation: str = "get_object", expires_in: int = 3600, **params
    ) -> str:
        """Generate a presigned URL for S3 operations"""
        try:
            url_params = {"Bucket": self.bucket_name, "Key": key}
            url_params.update(params)

            return self.s3.generate_presigned_url(
                operation,
                Params=url_params,
                ExpiresIn=expires_in,
            )
        except Exception as e:
            logger.error(f"Error generating presigned URL for {key}: {e}")
            raise

    def generate_download_url(
        self, job_id: str, format: str = "json", expires_in: int = 3600
    ) -> str:
        """Generate download URL for results"""
        # Determine file path based on format
        if format == "csv":
            key = f"results/{job_id}/hfo_events.csv"
            filename = f"{job_id}_hfo_events.csv"
        elif format == "report":
            key = f"results/{job_id}/analysis_report.csv"
            filename = f"{job_id}_comprehensive_report.csv"
        else:
            key = f"results/{job_id}/analysis_results.json"
            filename = f"{job_id}_results.json"

        return self.generate_presigned_url(
            key,
            "get_object",
            expires_in,
            ResponseContentDisposition=f'attachment; filename="{filename}"',
        )

    # Batch operations
    def check_batch_export_exists(self, batch_id: str) -> bool:
        """Check if batch export zip exists"""
        batch_key = f"results/batches/{batch_id}/results.zip"
        try:
            self.s3.head_object(Bucket=self.bucket_name, Key=batch_key)
            return True
        except ClientError:
            return False

    def generate_batch_export_url(
        self, batch_id: str, expires_in: int = 3600
    ) -> str | None:
        """Generate URL for batch export download"""
        if not self.check_batch_export_exists(batch_id):
            return None

        batch_key = f"results/batches/{batch_id}/results.zip"
        return self.generate_presigned_url(
            batch_key,
            "get_object",
            expires_in,
            ResponseContentDisposition=f'attachment; filename="{batch_id}_results.zip"',
        )
