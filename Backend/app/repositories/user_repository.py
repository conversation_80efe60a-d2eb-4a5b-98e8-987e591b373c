from datetime import datetime

from ..logging_config import get_logger
from .base_repository import BaseRepository

logger = get_logger(__name__)


class UserRepository(BaseRepository):
    """Repository for user-related DynamoDB operations"""

    def __init__(self):
        super().__init__()
        self.table = self.aws.preferences_table

    # Read operations
    def get_user(self, user_id: str) -> dict | None:
        """Get user preferences from DynamoDB"""
        if not self.table:
            return None

        try:
            response = self.table.get_item(Key={"user_id": user_id})
            return response.get("Item")
        except Exception as e:
            logger.error(f"Error getting user {user_id}: {e}")
            return None

    def get_user_email(self, user_id: str) -> str | None:
        """Get user's email address"""
        user = self.get_user(user_id)
        return user.get("email") if user else None

    # Write operations
    def create_or_update_user(
        self, user_id: str, email: str, notification_enabled: bool = True, **kwargs
    ) -> bool:
        """Create or update user preferences"""
        if not self.table:
            return False

        timestamp = datetime.utcnow().isoformat()

        try:
            # Check if user exists
            existing = self.get_user(user_id)

            if existing:
                # Update existing user
                self.table.update_item(
                    Key={"user_id": user_id},
                    UpdateExpression="SET email = :email, notification_enabled = :enabled, updated_at = :updated",
                    ExpressionAttributeValues={
                        ":email": email,
                        ":enabled": notification_enabled,
                        ":updated": timestamp,
                    },
                )
            else:
                # Create new user
                item = {
                    "user_id": user_id,
                    "email": email,
                    "notification_enabled": notification_enabled,
                    "created_at": timestamp,
                    "updated_at": timestamp,
                }
                item.update(kwargs)
                self.table.put_item(Item=item)

            return True
        except Exception as e:
            logger.error(f"Error updating user {user_id}: {e}")
            return False
