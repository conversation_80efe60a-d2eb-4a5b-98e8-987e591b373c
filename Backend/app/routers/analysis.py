"""
API endpoints for HFO analysis job management.
"""

from fastapi import APIRouter, Query
from pydantic import BaseModel

from ..logging_config import get_logger
from ..services.service_factory import get_analysis_service
from ..utils.error_decorators import handle_batch_errors, handle_service_errors

logger = get_logger(__name__)

router = APIRouter(prefix="/analysis", tags=["analysis"])


# Request/Response models
class AnalysisRequest(BaseModel):
    """Request model for submitting analysis job."""

    file_key: str
    parameters: dict | None = None


class BatchAnalysisRequest(BaseModel):
    """Request model for batch analysis."""

    file_keys: list[str]
    parameters: dict | None = None


class AnalysisResponse(BaseModel):
    """Response model for analysis submission."""

    job_id: str
    status: str
    message: str


class BatchAnalysisResponse(BaseModel):
    """Response model for batch analysis."""

    batch_id: str
    job_ids: list[str]
    status: str
    message: str


class JobStatus(BaseModel):
    """Job status response model."""

    job_id: str
    status: str
    file_key: str | None = None
    created_at: str
    updated_at: str | None = None
    completed_at: str | None = None
    hfo_count: int | None = None
    results_url: str | None = None
    error_message: str | None = None


@router.post("/submit", response_model=AnalysisResponse)
@handle_service_errors
async def submit_analysis(request: AnalysisRequest):
    """Submit a single EDF file for HFO analysis."""
    # Get service instance
    service = get_analysis_service()

    # Submit job
    result = await service.submit_single_job(
        file_key=request.file_key, parameters=request.parameters
    )

    return AnalysisResponse(**result)


@router.post("/batch", response_model=BatchAnalysisResponse)
@handle_batch_errors
async def submit_batch_analysis(request: BatchAnalysisRequest):
    """Submit multiple EDF files for batch HFO analysis."""
    # Get service instance
    service = get_analysis_service()

    # Submit batch
    result = await service.submit_batch_jobs(
        file_keys=request.file_keys, parameters=request.parameters
    )

    return BatchAnalysisResponse(**result)


@router.get("/jobs", response_model=list[JobStatus])
@handle_service_errors
async def list_user_jobs(user_email: str = Query(default="<EMAIL>")):
    """List all analysis jobs for a user."""
    # Get service instance
    service = get_analysis_service()

    # Get user jobs
    jobs = service.get_user_jobs(user_email)

    if not jobs:
        logger.info(f"No jobs found for user {user_email}")
        return []

    # Format response
    return [
        JobStatus(
            job_id=job["job_id"],
            status=job["status"],
            file_key=job.get("file_key"),
            created_at=job["created_at"],
            updated_at=job.get("updated_at"),
            completed_at=job.get("completed_at"),
            hfo_count=job.get("hfo_count"),
            results_url=job.get("results_url"),
            error_message=job.get("error_message"),
        )
        for job in jobs
    ]


@router.get("/status/{job_id}", response_model=JobStatus)
@handle_service_errors
async def get_job_status(job_id: str):
    """Get the status of an analysis job."""
    # Get service instance
    service = get_analysis_service()

    # Get job
    job = service.get_job_by_id(job_id)

    if not job:
        raise ValueError("Job not found")

    # Format response
    return JobStatus(
        job_id=job["job_id"],
        status=job["status"],
        file_key=job.get("file_key"),
        created_at=job["created_at"],
        updated_at=job.get("updated_at"),
        completed_at=job.get("completed_at"),
        hfo_count=job.get("hfo_count"),
        results_url=job.get("results_url"),
        error_message=job.get("error_message"),
    )


@router.get("/batch-status/{batch_id}")
@handle_service_errors
async def get_batch_status(batch_id: str):
    """Get the status of all jobs in a batch."""
    # Get service instance
    service = get_analysis_service()

    # Get batch jobs
    jobs = service.get_batch_jobs(batch_id)

    if not jobs:
        raise ValueError("Batch not found")

    # Get summary
    summary = service.summarize_batch_status(jobs)
    summary["batch_id"] = batch_id

    return summary


@router.get("/results/{job_id}")
@handle_service_errors
async def get_analysis_results(job_id: str):
    """Get the analysis results for a completed job."""
    # Get service instance
    service = get_analysis_service()

    # Get job status
    job = service.get_job_by_id(job_id)
    if not job:
        raise ValueError("Job not found")

    if job["status"] != "completed":
        raise ValueError(f"Job is not completed. Current status: {job['status']}")

    # Get results
    results = service.get_results_from_s3(job_id)
    return results


@router.get("/download/{job_id}")
@handle_service_errors
async def download_results(
    job_id: str, format: str = Query("json", regex="^(json|csv|report)$")
):
    """Get a download link for analysis results.

    Formats:
    - json: Complete results with all data
    - csv: Simple HFO events list
    - report: Comprehensive analysis report (CSV format)
    """
    # Get service instance
    service = get_analysis_service()

    # Get job status
    job = service.get_job_by_id(job_id)
    if not job:
        raise ValueError("Job not found")

    if job["status"] != "completed":
        raise ValueError(f"Job is not completed. Current status: {job['status']}")

    # Generate download URL
    download_url = service.generate_download_url(job_id, format)

    return {"download_url": download_url, "format": format}
