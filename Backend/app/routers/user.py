"""
API endpoints for user preferences management.
"""

from fastapi import APIRouter
from pydantic import BaseModel, Field, validator

from ..logging_config import get_logger
from ..services.service_factory import get_user_service
from ..services.user_service import UserService
from ..utils.error_decorators import handle_service_errors

logger = get_logger(__name__)

router = APIRouter(prefix="/user", tags=["user"])


# Request/Response models
class UserPreferences(BaseModel):
    """User preferences model."""

    user_id: str
    email: str
    notification_enabled: bool = True
    created_at: str | None = None
    updated_at: str | None = None

    @validator("email")
    def validate_email(cls, v):
        """Validate email format."""
        if not UserService.validate_email_format(v):
            raise ValueError("Invalid email format")
        return v.lower()


class UpdatePreferencesRequest(BaseModel):
    """Request model for updating preferences."""

    email: str = Field(..., description="Notification email address")
    notification_enabled: bool = Field(True, description="Enable email notifications")

    @validator("email")
    def validate_email(cls, v):
        """Validate email format."""
        if not UserService.validate_email_format(v):
            raise ValueError("Invalid email format")
        return v.lower()


class PreferencesResponse(BaseModel):
    """Response model for preferences."""

    email: str
    notification_enabled: bool
    last_updated: str


@router.get("/preferences", response_model=PreferencesResponse)
@handle_service_errors
async def get_user_preferences(user_id: str = "default_user"):
    """Get user preferences including email settings."""
    # Get service instance
    user_service = get_user_service()

    # Get preferences
    preferences = user_service.get_user_preferences(user_id)
    return PreferencesResponse(**preferences)


@router.put("/preferences", response_model=PreferencesResponse)
@handle_service_errors
async def update_user_preferences(
    request: UpdatePreferencesRequest, user_id: str = "default_user"
):
    """Update user preferences including email settings."""
    # Get service instance
    user_service = get_user_service()

    # Update preferences
    preferences = user_service.update_user_preferences(
        user_id=user_id,
        email=request.email,
        notification_enabled=request.notification_enabled,
    )

    return PreferencesResponse(**preferences)


@router.post("/preferences/verify-email")
@handle_service_errors
async def verify_email(email: str, user_id: str = "default_user"):
    """Verify email address (send test email)."""
    # Get service instance
    user_service = get_user_service()

    # Validate email
    if not UserService.validate_email_format(email):
        raise ValueError("Invalid email format")

    # Send verification email
    result = user_service.send_verification_email(email)
    return result


@router.get("/stats")
@handle_service_errors
async def get_user_stats(user_id: str = "default_user"):
    """Get user analysis statistics."""
    # Get service instance
    user_service = get_user_service()

    # Get stats
    return user_service.get_user_statistics(user_id)
