from ..logging_config import get_logger
from .batch_service import BatchService
from .job_service import JobService
from .results_service import ResultsService

logger = get_logger(__name__)


class AnalysisService:
    """High-level orchestration service for HFO analysis jobs"""

    def __init__(self):
        # Service dependencies
        self.job_service = JobService()
        self.batch_service = BatchService()
        self.results_service = ResultsService()

    # Single job operations
    async def submit_single_job(
        self,
        file_key: str,
        parameters: dict | None = None,
        user_email: str | None = None,
    ) -> dict:
        """Submit a single analysis job"""
        return await self.job_service.create_single_job(
            file_key=file_key,
            parameters=parameters,
            user_email=user_email,
        )

    # Batch operations
    async def submit_batch_jobs(
        self,
        file_keys: list[str],
        parameters: dict | None = None,
        user_email: str | None = None,
    ) -> dict:
        """Submit multiple analysis jobs as a batch"""
        return await self.batch_service.submit_batch_jobs(
            file_keys=file_keys,
            parameters=parameters,
            user_email=user_email,
        )

    # Job status operations
    def get_job_by_id(self, job_id: str) -> dict | None:
        """Get job details from DynamoDB"""
        return self.job_service.get_job(job_id)

    def get_user_jobs(self, user_email: str) -> list[dict]:
        """Get all jobs for a specific user"""
        return self.job_service.get_user_jobs(user_email)

    def get_batch_jobs(self, batch_id: str) -> list[dict]:
        """Get all jobs in a batch"""
        return self.job_service.get_batch_jobs(batch_id)

    def summarize_batch_status(self, jobs: list[dict]) -> dict:
        """Summarize the status of batch jobs"""
        batch_id = jobs[0].get("batch_id") if jobs else None
        return self.batch_service.summarize_batch_status(jobs, batch_id)

    # Results operations
    def get_results_from_s3(self, job_id: str) -> dict:
        """Get analysis results from S3"""
        results = self.results_service.get_results(job_id)
        if not results:
            raise ValueError(f"Results not found for job {job_id}")
        return results

    def generate_download_url(self, job_id: str, format: str = "json") -> str:
        """Generate presigned URL for downloading results"""
        return self.results_service.generate_download_url(job_id, format)

    # Batch operations
    async def retry_failed_batch_jobs(self, batch_id: str) -> dict:
        """Retry failed jobs in a batch"""
        return await self.batch_service.retry_failed_jobs(batch_id)
