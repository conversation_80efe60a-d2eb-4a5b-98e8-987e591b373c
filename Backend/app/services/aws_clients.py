"""
Centralized AWS client initialization and management.
"""

import os
from functools import lru_cache
from typing import Optional

import boto3
from botocore.exceptions import NoCredentialsError

from ..config import settings
from ..logging_config import get_logger

logger = get_logger(__name__)


class AWSClients:
    """Singleton for AWS client management."""

    _instance: Optional["AWSClients"] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """Initialize AWS clients."""
        try:
            # Core clients
            self.s3 = boto3.client("s3")
            self.dynamodb = boto3.resource("dynamodb")
            self.sqs = boto3.client("sqs")
            self.ses = boto3.client("ses", region_name="us-east-1")

            # Table references
            self._init_tables()

            # Queue URLs
            self.sqs_queue_url = os.getenv("SQS_QUEUE_URL", "")

            # S3 bucket
            self.s3_bucket_name = settings.s3_bucket_name

        except NoCredentialsError:
            logger.error("AWS credentials not configured")
            raise
        except Exception as e:
            logger.error(f"Error initializing AWS clients: {e}")
            raise

    def _init_tables(self):
        """Initialize DynamoDB table references."""
        jobs_table_name = os.getenv("JOBS_TABLE_NAME", "biormika-analysis-jobs")
        preferences_table_name = os.getenv(
            "PREFERENCES_TABLE_NAME", "biormika-user-preferences"
        )

        self.jobs_table = (
            self.dynamodb.Table(jobs_table_name) if jobs_table_name else None
        )
        self.preferences_table = (
            self.dynamodb.Table(preferences_table_name)
            if preferences_table_name
            else None
        )

    @property
    def is_configured(self) -> bool:
        """Check if AWS services are properly configured."""
        return bool(self.s3_bucket_name and self.jobs_table and self.preferences_table)


@lru_cache(maxsize=1)
def get_aws_clients() -> AWSClients:
    """Get singleton AWS clients instance."""
    return AWSClients()
