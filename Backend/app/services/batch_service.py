import uuid

from ..logging_config import get_logger
from .job_service import JobService

logger = get_logger(__name__)


class BatchService:
    """Service for batch job processing and orchestration"""

    def __init__(self):
        # Service dependency
        self.job_service = JobService()

    # Batch submission
    async def submit_batch_jobs(
        self,
        file_keys: list[str],
        parameters: dict | None = None,
        user_email: str | None = None,
    ) -> dict:
        """Submit multiple analysis jobs as a batch"""
        # Generate batch ID
        batch_id = str(uuid.uuid4())

        # Create all jobs
        jobs = await self.job_service.create_batch_jobs(
            file_keys=file_keys,
            parameters=parameters,
            user_email=user_email,
            batch_id=batch_id,
        )

        # Queue all jobs
        successful, failed = await self.job_service.queue_batch_jobs(jobs)

        if failed > 0:
            logger.warning(
                f"Batch {batch_id}: {failed} jobs failed to queue out of {len(jobs)}"
            )

        logger.info(f"Submitted batch {batch_id} with {len(jobs)} jobs")

        return {
            "batch_id": batch_id,
            "job_ids": [job["job_id"] for job in jobs],
            "status": "pending",
            "message": f"Batch analysis submitted with {len(jobs)} files",
        }

    # Batch status
    def get_batch_status(self, batch_id: str) -> dict | None:
        """Get status summary for all jobs in a batch"""
        jobs = self.job_service.get_batch_jobs(batch_id)

        if not jobs:
            return None

        return self.summarize_batch_status(jobs, batch_id)

    def summarize_batch_status(self, jobs: list[dict], batch_id: str) -> dict:
        """Summarize the status of batch jobs"""
        total = len(jobs)

        # Count job statuses
        status_counts = {
            "completed": sum(1 for j in jobs if j.get("status") == "completed"),
            "failed": sum(1 for j in jobs if j.get("status") == "failed"),
            "processing": sum(1 for j in jobs if j.get("status") == "processing"),
            "pending": sum(1 for j in jobs if j.get("status") == "pending"),
        }

        # Determine overall batch status
        batch_status = self._determine_batch_status(status_counts, total)

        # Create job summaries
        job_summaries = [
            {
                "job_id": j["job_id"],
                "file_key": j.get("file_key"),
                "status": j.get("status", "unknown"),
                "hfo_count": j.get("hfo_count"),
                "error": j.get("error_message"),
                "created_at": j.get("created_at"),
                "completed_at": j.get("completed_at"),
            }
            for j in jobs
        ]

        return {
            "batch_id": batch_id,
            "batch_status": batch_status,
            "total_jobs": total,
            **status_counts,
            "jobs": job_summaries,
        }

    # Batch operations
    async def retry_failed_jobs(self, batch_id: str) -> dict:
        """Retry all failed jobs in a batch"""
        jobs = self.job_service.get_batch_jobs(batch_id)
        failed_jobs = [j for j in jobs if j.get("status") == "failed"]

        if not failed_jobs:
            return {
                "batch_id": batch_id,
                "retried": 0,
                "message": "No failed jobs to retry",
            }

        # Queue failed jobs for retry
        successful, failed = await self.job_service.queue_batch_jobs(failed_jobs)

        return {
            "batch_id": batch_id,
            "retried": successful,
            "failed_to_retry": failed,
            "message": f"Retried {successful} failed jobs",
        }

    # Helper methods
    def _determine_batch_status(self, status_counts: dict, total: int) -> str:
        """Determine overall batch status from job counts"""
        if status_counts["failed"] == total:
            return "failed"
        elif status_counts["completed"] == total:
            return "completed"
        elif status_counts["completed"] + status_counts["failed"] == total:
            return "partial_complete"
        elif status_counts["processing"] > 0:
            return "processing"
        else:
            return "pending"
