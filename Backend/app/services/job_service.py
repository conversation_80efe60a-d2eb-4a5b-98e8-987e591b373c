import json
import uuid

from ..logging_config import get_logger
from ..repositories.job_repository import JobRepository
from ..repositories.queue_repository import QueueRepository
from ..repositories.s3_repository import S3Repository
from ..repositories.user_repository import UserRepository

logger = get_logger(__name__)


class JobService:
    """Consolidated service for job management"""

    DEFAULT_USER_EMAIL = "<EMAIL>"

    def __init__(self):
        # Repository dependencies
        self.job_repo = JobRepository()
        self.queue_repo = QueueRepository()
        self.s3_repo = S3Repository()
        self.user_repo = UserRepository()

    # Job creation
    async def create_single_job(
        self,
        file_key: str,
        parameters: dict | None = None,
        user_email: str | None = None,
    ) -> dict:
        """Create and queue a single analysis job"""
        # Validate file exists
        if not self.s3_repo.file_exists(file_key):
            raise ValueError(f"File not found: {file_key}")

        # Get user email if not provided
        if not user_email:
            user_email = await self._get_user_email_or_default("default_user")

        # Create job
        job_id = str(uuid.uuid4())
        job_data = self.job_repo.create_job(
            job_id=job_id,
            file_key=file_key,
            user_email=user_email,
            parameters=parameters,
        )

        # Queue job for processing
        queued = await self._queue_job(job_data)

        if not queued:
            logger.warning(f"Job {job_id} created but failed to queue")

        logger.info(f"Created and queued job: {job_id}")

        return {
            "job_id": job_id,
            "status": "pending",
            "message": "Analysis job submitted successfully",
        }

    async def create_batch_jobs(
        self,
        file_keys: list[str],
        parameters: dict | None = None,
        user_email: str | None = None,
        batch_id: str | None = None,
    ) -> list[dict]:
        """Create multiple jobs for batch processing"""
        # Validate files
        invalid_files = [key for key in file_keys if not self.s3_repo.file_exists(key)]
        if invalid_files:
            raise ValueError(f"Files not found: {', '.join(invalid_files)}")

        # Get user email
        if not user_email:
            user_email = await self._get_user_email_or_default("default_user")

        # Generate batch ID if not provided
        if not batch_id:
            batch_id = str(uuid.uuid4())

        # Create all jobs
        jobs = []
        for file_key in file_keys:
            job_id = str(uuid.uuid4())
            job_data = self.job_repo.create_job(
                job_id=job_id,
                file_key=file_key,
                user_email=user_email,
                parameters=parameters,
                batch_id=batch_id,
            )
            jobs.append(job_data)

        return jobs

    # Job retrieval
    def get_job(self, job_id: str) -> dict | None:
        """Get job details by ID"""
        return self.job_repo.get_job_by_id(job_id)

    def get_user_jobs(self, user_email: str) -> list[dict]:
        """Get all jobs for a user"""
        return self.job_repo.get_jobs_by_user(user_email)

    def get_batch_jobs(self, batch_id: str) -> list[dict]:
        """Get all jobs in a batch"""
        return self.job_repo.get_jobs_by_batch(batch_id)

    # Job queueing
    async def _queue_job(self, job_data: dict) -> bool:
        """Queue a single job for processing"""
        message = {
            "job_id": job_data["job_id"],
            "file_key": job_data["file_key"],
            "user_email": job_data["user_email"],
            "parameters": job_data.get("parameters", {}),
        }

        if "batch_id" in job_data:
            message["batch_id"] = job_data["batch_id"]

        return self.queue_repo.send_message(message)

    async def queue_batch_jobs(self, jobs: list[dict]) -> tuple[int, int]:
        """Queue multiple jobs for processing"""
        if not jobs:
            return 0, 0

        # Prepare messages for batch sending
        entries = []
        for job in jobs:
            message = {
                "job_id": job["job_id"],
                "file_key": job["file_key"],
                "user_email": job["user_email"],
                "parameters": job.get("parameters", {}),
            }
            if "batch_id" in job:
                message["batch_id"] = job["batch_id"]

            entries.append(
                {
                    "Id": job["job_id"],
                    "MessageBody": json.dumps(message),
                }
            )

        return self.queue_repo.send_batch(entries)

    # Job status updates
    def update_job_status(self, job_id: str, status: str, **kwargs) -> bool:
        """Update job status and metadata"""
        return self.job_repo.update_job_status(job_id, status, **kwargs)

    # Helper methods
    async def _get_user_email_or_default(self, user_id: str) -> str:
        """Get user email with fallback to default"""
        email = self.user_repo.get_user_email(user_id)
        return email if email else self.DEFAULT_USER_EMAIL

    # Validation
    def validate_files_exist(self, file_keys: list[str]) -> list[str]:
        """Return list of files that don't exist"""
        return [key for key in file_keys if not self.s3_repo.file_exists(key)]
