import json

from ..logging_config import get_logger
from ..repositories.queue_repository import QueueRepository

logger = get_logger(__name__)


class QueueService:
    """Service for SQS queue operations"""

    def __init__(self):
        # Repository dependency
        self.queue_repo = QueueRepository()

    # Queue operations
    async def send_job_to_queue(
        self,
        job_id: str,
        file_key: str,
        user_email: str,
        parameters: dict | None = None,
        batch_id: str | None = None,
    ) -> bool:
        """Send a single job to SQS queue"""
        message = {
            "job_id": job_id,
            "file_key": file_key,
            "user_email": user_email,
            "parameters": parameters or {},
        }

        if batch_id:
            message["batch_id"] = batch_id

        return self.queue_repo.send_message(message)

    async def send_batch_to_queue(self, messages: list[dict]) -> tuple[int, int]:
        """Send multiple jobs to SQS queue in batches"""
        return self.queue_repo.send_batch(messages)

    def prepare_batch_messages(self, jobs: list[dict]) -> list[dict]:
        """Prepare jobs for batch sending to SQS"""
        messages = []

        for job in jobs:
            message_body = {
                "job_id": job["job_id"],
                "file_key": job["file_key"],
                "user_email": job["user_email"],
                "parameters": job.get("parameters", {}),
            }

            if "batch_id" in job:
                message_body["batch_id"] = job["batch_id"]

            messages.append(
                {
                    "Id": job["job_id"],
                    "MessageBody": json.dumps(message_body),
                }
            )

        return messages

    # Queue monitoring
    async def get_queue_attributes(self) -> dict | None:
        """Get current queue attributes"""
        return self.queue_repo.get_queue_attributes()
