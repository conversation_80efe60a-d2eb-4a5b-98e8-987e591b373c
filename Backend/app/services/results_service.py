from ..logging_config import get_logger
from ..repositories.s3_repository import S3Repository

logger = get_logger(__name__)


class ResultsService:
    """Consolidated service for results management"""

    def __init__(self):
        # Repository dependency
        self.s3_repo = S3Repository()

    # Results retrieval
    def get_results(self, job_id: str) -> dict | None:
        """Get analysis results for a job"""
        results = self.s3_repo.get_results(job_id)

        if results:
            # Add download URLs
            results["download_urls"] = self._generate_download_urls(job_id)

        return results

    def results_exist(self, job_id: str) -> bool:
        """Check if results exist for a job"""
        return self.s3_repo.results_exist(job_id)

    # Download URL generation
    def generate_download_url(self, job_id: str, format: str = "json") -> str:
        """Generate presigned download URL for results"""
        return self.s3_repo.generate_download_url(job_id, format)

    def _generate_download_urls(self, job_id: str) -> dict:
        """Generate all download URLs for a job"""
        return {
            "results_json": self.generate_download_url(job_id, "json"),
            "hfo_events_csv": self.generate_download_url(job_id, "csv"),
            "analysis_report": self.generate_download_url(job_id, "report"),
        }

    # Results formatting
    def format_results_summary(self, results: dict) -> dict:
        """Format results into summary for API response"""
        metadata = results.get("metadata", {})
        statistics = results.get("statistics", {})

        return {
            "filename": metadata.get("filename", "Unknown"),
            "duration_seconds": metadata.get("duration_seconds", 0),
            "sampling_rate": metadata.get("sampling_rate", 0),
            "channels_analyzed": len(metadata.get("channels", [])),
            "total_hfos": statistics.get("total_hfos", 0),
            "hfo_density": statistics.get("hfo_density", 0),
            "channels_with_hfos": len(statistics.get("channels_with_hfos", [])),
            "processing_time": metadata.get("processing_time", 0),
        }

    def format_channel_summary(self, results: dict) -> list[dict]:
        """Format channel-specific results"""
        statistics = results.get("statistics", {})
        hfo_rate = statistics.get("hfo_rate_per_channel", {})

        summaries = []
        for channel, count in hfo_rate.items():
            summaries.append(
                {
                    "channel": channel,
                    "hfo_count": count,
                    "has_hfos": count > 0,
                }
            )

        # Sort by HFO count descending
        summaries.sort(key=lambda x: x["hfo_count"], reverse=True)
        return summaries

    # Batch results
    async def get_batch_results(self, batch_id: str, job_ids: list[str]) -> dict:
        """Get results for all completed jobs in a batch"""
        results = {
            "batch_id": batch_id,
            "total_jobs": len(job_ids),
            "results_available": 0,
            "job_results": {},
        }

        for job_id in job_ids:
            job_results = self.get_results(job_id)
            if job_results:
                results["job_results"][job_id] = self.format_results_summary(
                    job_results
                )
                results["results_available"] += 1

        return results

    def generate_batch_export_url(self, batch_id: str) -> str | None:
        """Generate download URL for batch results zip"""
        return self.s3_repo.generate_batch_export_url(batch_id)
