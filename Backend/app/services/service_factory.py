from functools import lru_cache

from ..logging_config import get_logger
from .analysis_service import AnalysisService
from .batch_service import BatchService
from .job_service import JobService
from .queue_service import QueueService
from .results_service import ResultsService
from .user_service import UserService

logger = get_logger(__name__)


class ServiceFactory:
    """Factory for creating and managing service instances"""

    _instances = {}

    @classmethod
    def get_analysis_service(cls) -> AnalysisService:
        """Get or create AnalysisService instance"""
        if "analysis" not in cls._instances:
            cls._instances["analysis"] = AnalysisService()
        return cls._instances["analysis"]

    @classmethod
    def get_job_service(cls) -> JobService:
        """Get or create JobService instance"""
        if "job" not in cls._instances:
            cls._instances["job"] = JobService()
        return cls._instances["job"]

    @classmethod
    def get_batch_service(cls) -> BatchService:
        """Get or create BatchService instance"""
        if "batch" not in cls._instances:
            cls._instances["batch"] = BatchService()
        return cls._instances["batch"]

    @classmethod
    def get_results_service(cls) -> ResultsService:
        """Get or create ResultsService instance"""
        if "results" not in cls._instances:
            cls._instances["results"] = ResultsService()
        return cls._instances["results"]

    @classmethod
    def get_queue_service(cls) -> QueueService:
        """Get or create QueueService instance"""
        if "queue" not in cls._instances:
            cls._instances["queue"] = QueueService()
        return cls._instances["queue"]

    @classmethod
    def get_user_service(cls) -> UserService:
        """Get or create UserService instance"""
        if "user" not in cls._instances:
            cls._instances["user"] = UserService()
        return cls._instances["user"]

    @classmethod
    def clear_instances(cls):
        """Clear all cached instances (useful for testing)"""
        cls._instances.clear()


# Convenience functions for getting services
@lru_cache(maxsize=1)
def get_analysis_service() -> AnalysisService:
    """Get AnalysisService instance"""
    return ServiceFactory.get_analysis_service()


@lru_cache(maxsize=1)
def get_job_service() -> JobService:
    """Get JobService instance"""
    return ServiceFactory.get_job_service()


@lru_cache(maxsize=1)
def get_batch_service() -> BatchService:
    """Get BatchService instance"""
    return ServiceFactory.get_batch_service()


@lru_cache(maxsize=1)
def get_results_service() -> ResultsService:
    """Get ResultsService instance"""
    return ServiceFactory.get_results_service()


@lru_cache(maxsize=1)
def get_queue_service() -> QueueService:
    """Get QueueService instance"""
    return ServiceFactory.get_queue_service()


@lru_cache(maxsize=1)
def get_user_service() -> UserService:
    """Get UserService instance"""
    return ServiceFactory.get_user_service()
