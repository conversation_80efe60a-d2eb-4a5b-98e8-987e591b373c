import re
from datetime import datetime
from typing import Any

from botocore.exceptions import Client<PERSON>rror

from ..logging_config import get_logger
from ..repositories.user_repository import UserRepository
from ..services.aws_clients import get_aws_clients

logger = get_logger(__name__)

# Configuration
SES_SENDER_EMAIL = "<EMAIL>"
EMAIL_REGEX = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"


class UserService:
    """Service for user preference and notification management"""

    def __init__(self):
        # Repository dependency
        self.user_repo = UserRepository()
        # SES client for email operations
        self.aws = get_aws_clients()

    # Email validation
    @staticmethod
    def validate_email_format(email: str) -> bool:
        """Validate email format"""
        return bool(re.match(EMAIL_REGEX, email))

    # User preferences
    def get_user_preferences(self, user_id: str) -> dict[str, Any]:
        """Get user preferences from DynamoDB"""
        user = self.user_repo.get_user(user_id)

        if not user:
            return {
                "email": "",
                "notification_enabled": True,
                "last_updated": datetime.utcnow().isoformat(),
            }

        return {
            "email": user.get("email", ""),
            "notification_enabled": user.get("notification_enabled", True),
            "last_updated": user.get("updated_at", user.get("created_at", "")),
        }

    def update_user_preferences(
        self, user_id: str, email: str, notification_enabled: bool
    ) -> dict[str, Any]:
        """Update user preferences in DynamoDB"""
        success = self.user_repo.create_or_update_user(
            user_id=user_id,
            email=email,
            notification_enabled=notification_enabled,
        )

        if not success:
            raise ValueError("Failed to update preferences")

        return {
            "email": email,
            "notification_enabled": notification_enabled,
            "last_updated": datetime.utcnow().isoformat(),
        }

    # Email notifications
    def send_verification_email(self, email: str) -> dict[str, str]:
        """Send verification email using SES"""
        try:
            self.aws.ses.send_email(
                Source=SES_SENDER_EMAIL,
                Destination={"ToAddresses": [email]},
                Message={
                    "Subject": {"Data": "Biormika - Email Verification"},
                    "Body": {
                        "Text": {
                            "Data": "Your email has been verified for Biormika HFO Analysis notifications.\n\n"
                            "You will receive notifications when your analysis jobs complete."
                        }
                    },
                },
            )

            logger.info(f"Verification email sent to {email}")

            return {
                "status": "success",
                "message": "Verification email sent. Please check your inbox.",
            }

        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            error_message = e.response["Error"].get("Message", "")

            if error_code == "MessageRejected":
                if "not verified" in error_message.lower():
                    logger.warning(f"SES sandbox mode: {error_message}")
                    raise ValueError(
                        "Email address not verified in SES sandbox. "
                        "Please contact support or verify your email address."
                    ) from e
                else:
                    raise ValueError(f"Email rejected: {error_message}") from e
            elif error_code == "ConfigurationSet":
                raise ValueError(
                    "Email service configuration error. Please contact support."
                ) from e
            else:
                logger.error(f"SES error: {error_code} - {error_message}")
                raise ValueError(f"Failed to send email: {error_code}") from e

    # User statistics
    def get_user_statistics(self, user_id: str) -> dict[str, int]:
        """Get user analysis statistics"""
        # Currently returns empty stats
        # Could aggregate from jobs table in future
        return {
            "total_analyses": 0,
            "hfos_detected": 0,
        }
