from collections.abc import Callable
from functools import wraps

from botocore.exceptions import ClientError
from fastapi import HTTPException

from ..logging_config import get_logger

logger = get_logger(__name__)


def handle_service_errors(func: Callable) -> Callable:
    """Decorator to handle common service errors"""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except ValueError as e:
            # File not found or validation errors
            raise HTTPException(status_code=404, detail=str(e)) from e
        except ClientError as e:
            # AWS service errors
            error_code = e.response["Error"]["Code"]
            if error_code == "NoSuchKey":
                raise HTTPException(status_code=404, detail="Resource not found") from e
            logger.error(f"AWS error in {func.__name__}: {e}")
            raise HTTPException(status_code=500, detail="Service error occurred") from e
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            # Unexpected errors
            logger.error(f"Unexpected error in {func.__name__}: {e}")
            raise HTTPException(status_code=500, detail=str(e)) from e

    return wrapper


def handle_batch_errors(func: Callable) -> Callable:
    """Decorator for batch operation error handling"""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except ValueError as e:
            # Validation errors for batch operations
            raise HTTPException(status_code=400, detail=str(e)) from e
        except Exception as e:
            logger.error(f"Batch operation error in {func.__name__}: {e}")
            raise HTTPException(status_code=500, detail=str(e)) from e

    return wrapper
