"""
Validation utilities for file operations
"""

from pydantic import field_validator

from .constants import ALLOWED_EXTENSIONS, MAX_FILE_SIZE_BYTES


class FileValidators:
    """Common validators for file operations"""

    @staticmethod
    def validate_filename(v: str) -> str:
        """Validate file has allowed extension"""
        if not v.lower().endswith(ALLOWED_EXTENSIONS):
            raise ValueError("Only .edf files are allowed")
        return v

    @staticmethod
    def validate_filesize(v: int) -> int:
        """Validate file size is within limits"""
        if v > MAX_FILE_SIZE_BYTES:
            raise ValueError("File too large. Maximum size is 1GB")
        if v <= 0:
            raise ValueError("File size must be greater than 0")
        return v


def create_filename_validator():
    """Create a Pydantic field validator for filename"""

    @field_validator("filename")
    @classmethod
    def validator(cls, v):
        return FileValidators.validate_filename(v)

    return validator


def create_filesize_validator():
    """Create a Pydantic field validator for filesize"""

    @field_validator("filesize")
    @classmethod
    def validator(cls, v):
        return FileValidators.validate_filesize(v)

    return validator
