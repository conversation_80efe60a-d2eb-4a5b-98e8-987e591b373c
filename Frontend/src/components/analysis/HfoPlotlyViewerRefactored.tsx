// Refactored HFO Plotly viewer component
import React, { useMemo, useState, useRef, useCallback } from 'react';
import Plot from 'react-plotly.js';
import * as Plotly from 'plotly.js';
import { Card } from '@/components/ui/card';
import { PlotControlPanel } from './PlotControlPanel';
import { ChannelSelectionPanel } from './ChannelSelectionPanel';
import { PlotDataService } from '@/services/plotDataService';
import { useTimeWindow } from '@/hooks/useTimeWindow';
import { useChannelSelection } from '@/hooks/useChannelSelection';
import { useHfoAnalysis } from '@/hooks/useHfoAnalysis';
import { usePlotNavigationShortcuts } from '@/hooks/useKeyboardShortcuts';
import { calculateChannelOffset } from '@/utils/plotUtils';
import type { HFOEvent } from '@/types/hfo';

interface HfoPlotlyViewerProps {
  channelData: Record<string, number[]>;
  hfoEvents: HFOEvent[];
  samplingRate: number;
  duration: number;
  channelLabels: string[];
  metadata?: {
    filename?: string;
    montage?: string;
    frequency_band?: string;
  };
}

export const HfoPlotlyViewerRefactored: React.FC<HfoPlotlyViewerProps> = React.memo(({
  channelData,
  hfoEvents,
  samplingRate,
  duration,
  channelLabels,
  metadata
}) => {
  // State management
  const [gain, setGain] = useState(20);
  const [showHFOMarkers, setShowHFOMarkers] = useState(true);
  const [showThresholds, setShowThresholds] = useState(false);
  const [isPanelCollapsed, setIsPanelCollapsed] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Use custom hooks for time window management
  const {
    timeWindow,
    setTimeWindow,
    zoomIn,
    zoomOut,
    navigate,
    reset
  } = useTimeWindow({
    duration,
    defaultWindowSize: 10
  });

  // Use custom hooks for channel selection
  const {
    selectedChannels,
    toggleChannel,
    selectAll,
    clearAll
  } = useChannelSelection({
    allChannels: channelLabels,
    initialSelection: channelLabels
  });

  // Use HFO analysis hook
  const {
    hfoCountPerChannel,
    hfoEventsInWindow
  } = useHfoAnalysis({
    hfoEvents,
    channels: channelLabels,
    timeWindow
  });

  // Fullscreen handler
  const handleFullscreen = useCallback(() => {
    if (!document.fullscreenElement && containerRef.current) {
      containerRef.current.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Export handler
  const handleExport = useCallback(() => {
    const plotElement = document.querySelector('.js-plotly-plot') as HTMLElement & {
      _fullLayout?: unknown;
      _fullData?: unknown;
    };
    if (plotElement) {
      Plotly.downloadImage(plotElement, {
        format: 'png',
        width: 1920,
        height: 1200,
        filename: `hfo_analysis_${metadata?.filename?.replace('.edf', '') || 'export'}_${new Date().toISOString().split('T')[0]}`
      });
    }
  }, [metadata]);

  // Setup keyboard shortcuts
  usePlotNavigationShortcuts({
    onZoomIn: zoomIn,
    onZoomOut: zoomOut,
    onNavigatePrev: () => navigate('prev'),
    onNavigateNext: () => navigate('next'),
    onFullscreen: handleFullscreen,
    onReset: reset,
    enabled: true
  });

  // Calculate channel offset
  const channelOffset = useMemo(
    () => calculateChannelOffset(selectedChannels.length, gain),
    [selectedChannels.length, gain]
  );

  // Generate plot data using service
  const plotData = useMemo(() => {
    return PlotDataService.generatePlotData({
      channelData,
      selectedChannels,
      hfoEvents,
      timeWindow,
      samplingRate,
      gain,
      showHFOMarkers,
      showThresholds
    });
  }, [
    channelData,
    selectedChannels,
    hfoEvents,
    timeWindow,
    samplingRate,
    gain,
    showHFOMarkers,
    showThresholds
  ]);

  // Generate layout using service
  const layout = useMemo(() => {
    const height = Math.max(600, selectedChannels.length * 80 + 150);
    return PlotDataService.generateLayout(
      selectedChannels,
      timeWindow,
      channelOffset,
      height,
      metadata
    );
  }, [selectedChannels, timeWindow, channelOffset, metadata]);

  // Generate config using service
  const config = useMemo(
    () => PlotDataService.generateConfig(metadata),
    [metadata]
  );

  // Fullscreen change listener
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  return (
    <div className="space-y-4" ref={containerRef}>
      {/* Control Panel */}
      <PlotControlPanel
        timeWindow={timeWindow}
        onTimeWindowChange={setTimeWindow}
        duration={duration}
        onZoomIn={zoomIn}
        onZoomOut={zoomOut}
        onReset={reset}
        gain={gain}
        onGainChange={setGain}
        showHFOMarkers={showHFOMarkers}
        onToggleHFOMarkers={() => setShowHFOMarkers(!showHFOMarkers)}
        showThresholds={showThresholds}
        onToggleThresholds={() => setShowThresholds(!showThresholds)}
        onNavigate={navigate}
        onExport={handleExport}
        onFullscreen={handleFullscreen}
        isFullscreen={isFullscreen}
        showKeyboardHints={true}
      />

      {/* Main plot with channel selection */}
      <div className="flex gap-4" style={{ maxHeight: '80vh', overflow: 'hidden' }}>
        {/* Channel Selection Panel */}
        <ChannelSelectionPanel
          channels={channelLabels}
          selectedChannels={selectedChannels}
          onChannelToggle={toggleChannel}
          onSelectAll={selectAll}
          onClearAll={clearAll}
          hfoCountPerChannel={hfoCountPerChannel}
          isCollapsed={isPanelCollapsed}
          onToggleCollapse={() => setIsPanelCollapsed(!isPanelCollapsed)}
        />

        {/* Main plot */}
        <Card className="flex-1 p-4 overflow-auto">
          <div style={{ minHeight: `${Math.max(600, selectedChannels.length * 80 + 150)}px` }}>
            <Plot
              data={plotData}
              layout={layout}
              config={config}
              useResizeHandler
              style={{ width: '100%', height: '100%' }}
            />
          </div>
        </Card>
      </div>

      {/* HFO statistics */}
      <Card className="p-4">
        <div className="flex justify-between text-sm">
          <div>
            <span className="text-gray-600">Total HFOs in view: </span>
            <span className="font-semibold">{hfoEventsInWindow.length}</span>
          </div>
          <div>
            <span className="text-gray-600">Channels displayed: </span>
            <span className="font-semibold">{selectedChannels.length}</span>
          </div>
          <div>
            <span className="text-gray-600">Sampling rate: </span>
            <span className="font-semibold">{samplingRate} Hz</span>
          </div>
        </div>
      </Card>
    </div>
  );
});