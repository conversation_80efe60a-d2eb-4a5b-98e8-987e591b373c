// Refactored Results Viewer component
import React from 'react';
import { FileText, BarChart3, Table, FileDown } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ResultsSummaryTab } from './tabs/ResultsSummaryTab';
import { ResultsGraphTab } from './tabs/ResultsGraphTab';
import { ResultsDownloadTab } from './tabs/ResultsDownloadTab';
import { HfoEventsTable } from './HfoEventsTable';
import { useAnalysisResults } from '@/hooks/useAnalysisResults';
import { useHfoAnalysis } from '@/hooks/useHfoAnalysis';

interface ResultsViewerProps {
  jobId: string;
  onClose?: () => void;
}

export const ResultsViewerRefactored: React.FC<ResultsViewerProps> = ({
  jobId,
  onClose
}) => {
  // Use custom hook for data fetching
  const {
    results,
    loading,
    error,
    downloadResults
  } = useAnalysisResults(jobId);

  // Use HFO analysis hook for filtering
  const {
    filteredHfoEvents
  } = useHfoAnalysis({
    hfoEvents: results?.hfo_events || [],
    channels: results?.metadata.channels || []
  });

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading analysis results...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <p className="text-red-800">{error}</p>
      </div>
    );
  }

  if (!results) return null;

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center border-b pb-4">
        <div>
          <h2 className="text-2xl font-semibold">HFO Analysis Results</h2>
          <p className="text-gray-600">{results.metadata.filename}</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        )}
      </div>

      {/* Tabbed Interface */}
      <Tabs defaultValue="summary" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="summary">
            <FileText className="w-4 h-4 mr-2" />
            Summary
          </TabsTrigger>
          <TabsTrigger value="graph">
            <BarChart3 className="w-4 h-4 mr-2" />
            Graph View
          </TabsTrigger>
          <TabsTrigger value="events">
            <Table className="w-4 h-4 mr-2" />
            HFO Events
          </TabsTrigger>
          <TabsTrigger value="report">
            <FileDown className="w-4 h-4 mr-2" />
            Report
          </TabsTrigger>
        </TabsList>

        {/* Summary Tab */}
        <TabsContent value="summary">
          <ResultsSummaryTab results={results} />
        </TabsContent>

        {/* Graph View Tab */}
        <TabsContent value="graph">
          <ResultsGraphTab results={results} />
        </TabsContent>

        {/* HFO Events Tab */}
        <TabsContent value="events">
          <HfoEventsTable events={filteredHfoEvents} />
        </TabsContent>

        {/* Report Tab */}
        <TabsContent value="report">
          <ResultsDownloadTab
            results={results}
            jobId={jobId}
            downloadResults={downloadResults}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};