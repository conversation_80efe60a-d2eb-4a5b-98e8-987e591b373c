// Results download tab component
import React from 'react';
import { Download, FileText, Table, FileDown } from 'lucide-react';
import { Card } from '@/components/ui/card';
import {
  downloadResultsAsJSON,
  downloadResultsAsCSV,
  downloadFile,
  generateComprehensiveReport
} from '@/utils/downloadUtils';
import type { AnalysisResults } from '@/hooks/useAnalysisResults';

interface ResultsDownloadTabProps {
  results: AnalysisResults;
  jobId: string;
  downloadResults: (format: 'json' | 'csv' | 'report') => Promise<boolean>;
}

export const ResultsDownloadTab: React.FC<ResultsDownloadTabProps> = ({
  results,
  jobId,
  downloadResults
}) => {
  const handleDownload = async (format: 'json' | 'csv' | 'report') => {
    // Try server download first
    const success = await downloadResults(format);

    // Fall back to local download if server fails
    if (!success) {
      if (format === 'json') {
        downloadResultsAsJSON(results, jobId);
      } else if (format === 'csv') {
        downloadResultsAsCSV(results, jobId);
      } else if (format === 'report') {
        const report = generateComprehensiveReport(results);
        downloadFile(report, `analysis_report_${jobId}.txt`, 'text/plain');
      }
    }
  };

  return (
    <div className="space-y-4">
      <Card className="p-6">
        <h3 className="font-semibold mb-4">Download Options</h3>
        <p className="text-sm text-gray-600 mb-6">
          Download analysis results in different formats
        </p>

        <div className="space-y-3">
          {/* Comprehensive Report */}
          <button
            onClick={() => handleDownload('report')}
            className="w-full flex items-center justify-between px-4 py-3 border-2 border-blue-500 text-blue-600 rounded-md hover:bg-blue-50"
          >
            <div className="flex items-center">
              <FileText className="w-5 h-5 mr-3" />
              <div className="text-left">
                <p className="font-medium">Comprehensive Report</p>
                <p className="text-sm text-gray-500">
                  Full analysis report with demographics and HFO profiles
                </p>
              </div>
            </div>
            <Download className="w-4 h-4" />
          </button>

          {/* HFO Events CSV */}
          <button
            onClick={() => handleDownload('csv')}
            className="w-full flex items-center justify-between px-4 py-3 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <div className="flex items-center">
              <Table className="w-5 h-5 mr-3" />
              <div className="text-left">
                <p className="font-medium">HFO Events (CSV)</p>
                <p className="text-sm text-gray-500">
                  Simple list of detected HFO events
                </p>
              </div>
            </div>
            <Download className="w-4 h-4" />
          </button>

          {/* Raw JSON */}
          <button
            onClick={() => handleDownload('json')}
            className="w-full flex items-center justify-between px-4 py-3 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <div className="flex items-center">
              <FileDown className="w-5 h-5 mr-3" />
              <div className="text-left">
                <p className="font-medium">Raw Data (JSON)</p>
                <p className="text-sm text-gray-500">
                  Complete results with all channel data
                </p>
              </div>
            </div>
            <Download className="w-4 h-4" />
          </button>
        </div>
      </Card>

      {/* Download Information */}
      <Card className="p-4 bg-gray-50">
        <h4 className="text-sm font-medium text-gray-700 mb-2">
          Download Information
        </h4>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• Comprehensive Report: Complete analysis with all statistics</li>
          <li>• CSV Format: Compatible with Excel and statistical software</li>
          <li>• JSON Format: Preserves all data structure for reprocessing</li>
          <li>• All downloads include metadata and analysis parameters</li>
        </ul>
      </Card>
    </div>
  );
};