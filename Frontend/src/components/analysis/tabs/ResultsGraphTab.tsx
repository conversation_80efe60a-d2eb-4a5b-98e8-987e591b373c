// Results graph visualization tab component
import React, { useState, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ChannelGrid } from '../ChannelGrid';
import { ChannelSelector } from '../ChannelSelector';
import { TimeNavigationControls } from '../TimeNavigationControls';
import { HfoPlotlyViewerRefactored } from '../HfoPlotlyViewerRefactored';
import { useTimeWindow } from '@/hooks/useTimeWindow';
import { useChannelSelection } from '@/hooks/useChannelSelection';
import { useHfoAnalysis } from '@/hooks/useHfoAnalysis';
import type { AnalysisResults } from '@/hooks/useAnalysisResults';
import type { HFOType } from '@/types/hfo';
import { HFO_TYPE_COLORS, HFO_TYPE_LABELS } from '@/types/hfo';

interface ResultsGraphTabProps {
  results: AnalysisResults;
}

export const ResultsGraphTab: React.FC<ResultsGraphTabProps> = ({ results }) => {
  const [usePlotlyRows, setUsePlotlyRows] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const channelViewRef = useRef<HTMLDivElement>(null);

  // Use custom hooks
  const {
    timeWindow,
    setTimeWindow,
    windowSize,
    setWindowSize,
    reset
  } = useTimeWindow({
    duration: results.metadata.duration_seconds,
    defaultWindowSize: 10
  });

  const {
    selectedChannels,
    toggleChannel,
    selectAll,
    clearAll
  } = useChannelSelection({
    allChannels: results.metadata.channels,
    initialSelection: results.metadata.channels
  });

  const {
    visibleHfoTypes,
    toggleHfoType,
    hfoStatsByType,
    filteredHfoEvents,
    hfoCountPerChannel
  } = useHfoAnalysis({
    hfoEvents: results.hfo_events,
    channels: results.metadata.channels,
    timeWindow
  });

  // Fullscreen handler
  const handleFullscreen = () => {
    if (channelViewRef.current) {
      if (!document.fullscreenElement) {
        channelViewRef.current.requestFullscreen();
        setIsFullscreen(true);
      } else {
        document.exitFullscreen();
        setIsFullscreen(false);
      }
    }
  };

  // Fullscreen change listener
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  return (
    <div className="space-y-4">
      {/* HFO Type Filters */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-sm">HFO Event Filters</h3>
          <div className="flex items-center gap-2 text-xs">
            <button
              onClick={() => setUsePlotlyRows(!usePlotlyRows)}
              className="px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-700"
            >
              {usePlotlyRows ? 'Canvas Mode' : 'Plotly Mode'}
            </button>
            {hfoStatsByType && (
              <span className="text-gray-500">
                Showing {filteredHfoEvents.length} of {hfoStatsByType.total} events
              </span>
            )}
          </div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
          {(['accepted', 'rejected', 'rejected_long', 'lfo_rejected', 'noise_rejected'] as HFOType[]).map((type) => {
            const count = hfoStatsByType ? hfoStatsByType[type as keyof typeof hfoStatsByType] : 0;
            const isVisible = visibleHfoTypes.includes(type);
            return (
              <label
                key={type}
                className="flex items-center space-x-2 cursor-pointer"
              >
                <Checkbox
                  checked={isVisible}
                  onCheckedChange={() => toggleHfoType(type)}
                />
                <div className="flex items-center gap-1">
                  <span
                    className="w-3 h-3 rounded"
                    style={{ backgroundColor: HFO_TYPE_COLORS[type] }}
                  />
                  <span className="text-xs text-gray-700">
                    {HFO_TYPE_LABELS[type]} ({count})
                  </span>
                </div>
              </label>
            );
          })}
        </div>
      </Card>

      {/* Time Navigation Controls */}
      <TimeNavigationControls
        timeWindow={timeWindow}
        timeWindowSize={windowSize}
        totalDuration={results.metadata.duration_seconds}
        onTimeWindowChange={setTimeWindow}
        onTimeWindowSizeChange={setWindowSize}
        onReset={() => {
          reset();
          selectAll();
        }}
        onFullscreen={handleFullscreen}
      />

      {/* Multi-Channel View */}
      <div
        ref={channelViewRef}
        className="flex border border-gray-200 rounded-lg overflow-hidden"
        style={{ height: '600px' }}
      >
        {/* Channel Selector Sidebar */}
        {!isFullscreen && (
          <ChannelSelector
            channels={results.metadata.channels}
            selectedChannels={selectedChannels}
            onChannelToggle={toggleChannel}
            onSelectAll={selectAll}
            onClearAll={clearAll}
            hfoCountByChannel={hfoCountPerChannel}
          />
        )}

        {/* Channel Grid Display */}
        <div className="flex-1 bg-white overflow-hidden">
          <ChannelGrid
            channelData={results.channel_data}
            visibleChannels={selectedChannels}
            timeWindow={timeWindow}
            samplingRate={results.metadata.sampling_rate}
            hfoEvents={filteredHfoEvents}
            showHFOMarkers={true}
            usePlotly={usePlotlyRows}
            visibleHFOTypes={visibleHfoTypes}
          />
        </div>
      </div>

      {/* Alternative Plotly View (collapsible) */}
      <details>
        <summary className="cursor-pointer text-sm text-gray-600">
          Alternative Plotly View
        </summary>
        <div className="mt-4">
          <HfoPlotlyViewerRefactored
            channelData={results.channel_data}
            hfoEvents={filteredHfoEvents}
            samplingRate={results.metadata.sampling_rate}
            duration={results.metadata.duration_seconds}
            channelLabels={results.metadata.channels}
            metadata={{
              filename: results.metadata.filename,
              montage: results.metadata.montage || "Bipolar",
              frequency_band: `${results.metadata.low_cutoff || 80}-${results.metadata.high_cutoff || 500} Hz`
            }}
          />
        </div>
      </details>
    </div>
  );
};