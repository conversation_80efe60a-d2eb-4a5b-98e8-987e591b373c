// Results summary tab component
import React from 'react';
import { Card } from '@/components/ui/card';
import { ResultsStatistics } from '../ResultsStatistics';
import { HfoDistributionChart } from '../HfoDistributionChart';
import type { AnalysisResults } from '@/hooks/useAnalysisResults';

interface ResultsSummaryTabProps {
  results: AnalysisResults;
}

export const ResultsSummaryTab: React.FC<ResultsSummaryTabProps> = ({ results }) => {
  return (
    <div className="space-y-6">
      {/* Statistics card */}
      <ResultsStatistics
        totalHfos={results.statistics.total_hfos}
        hfoDensity={results.statistics.hfo_density}
        channelCount={results.metadata.channels.length}
        durationSeconds={results.metadata.duration_seconds}
      />

      {/* HFO Distribution */}
      {results.statistics.hfo_rate_per_channel && (
        <HfoDistributionChart
          hfoRatePerChannel={results.statistics.hfo_rate_per_channel}
        />
      )}

      {/* Channel Summary */}
      <Card className="p-4">
        <h3 className="font-semibold mb-3">
          Channel Summary ({results.metadata.channels.length} channels)
        </h3>
        <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 text-sm max-h-96 overflow-y-auto">
          {results.metadata.channels.map((channel) => {
            const hfoCount = results.hfo_events.filter(h => h.channel === channel).length;
            return (
              <div
                key={channel}
                className="flex justify-between border-b border-gray-100 pb-1"
              >
                <span className="text-gray-600 text-xs truncate" title={channel}>
                  {channel}:
                </span>
                <span className="font-medium text-xs ml-1">
                  {hfoCount} HFOs
                </span>
              </div>
            );
          })}
        </div>
      </Card>
    </div>
  );
};