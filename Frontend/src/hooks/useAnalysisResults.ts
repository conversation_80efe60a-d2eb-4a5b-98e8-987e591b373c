// Hook for fetching and managing analysis results
import { useState, useCallback, useEffect } from 'react';
import { apiClient } from '@/services/api';
import type { HFOEvent } from '@/types/hfo';

export interface AnalysisResults {
  metadata: {
    filename: string;
    sampling_rate: number;
    duration_seconds: number;
    channels: string[];
    processing_time: number;
    montage?: string;
    low_cutoff?: number;
    high_cutoff?: number;
  };
  statistics: {
    total_hfos: number;
    hfo_density: number;
    channels_with_hfos: string[];
    hfo_rate_per_channel?: Record<string, number>;
  };
  channel_data: Record<string, number[]>;
  hfo_events: HFOEvent[];
  download_urls?: {
    results_json: string;
    hfo_events_csv: string;
  };
}

export const useAnalysisResults = (jobId: string) => {
  const [results, setResults] = useState<AnalysisResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchResults = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get<AnalysisResults>(`/analysis/results/${jobId}`);

      if (!response.data || !response.data.metadata) {
        throw new Error("Invalid results format");
      }

      setResults(response.data);
    } catch (err) {
      const error = err as { response?: { data?: { detail?: string } } };
      setError(error.response?.data?.detail || "Failed to load results");
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  // Download results in specified format
  const downloadResults = useCallback(async (format: 'json' | 'csv' | 'report') => {
    try {
      const response = await apiClient.get<{ download_url: string }>(
        `/analysis/download/${jobId}?format=${format}`
      );

      if (response.data && response.data.download_url) {
        window.open(response.data.download_url, "_blank");
        return true;
      }
      return false;
    } catch {
      return false;
    }
  }, [jobId]);

  // Initial fetch
  useEffect(() => {
    fetchResults();
  }, [fetchResults]);

  return {
    results,
    loading,
    error,
    refetch: fetchResults,
    downloadResults
  };
};