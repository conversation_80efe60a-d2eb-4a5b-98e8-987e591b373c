// Channel selection management hook
import { useState, useCallback, useMemo } from 'react';

interface UseChannelSelectionOptions {
  allChannels: string[];
  initialSelection?: string[];
}

export const useChannelSelection = ({
  allChannels,
  initialSelection
}: UseChannelSelectionOptions) => {
  const [selectedChannels, setSelectedChannels] = useState<string[]>(
    initialSelection || allChannels
  );

  // Toggle individual channel
  const toggleChannel = useCallback((channel: string) => {
    setSelectedChannels(prev =>
      prev.includes(channel)
        ? prev.filter(ch => ch !== channel)
        : [...prev, channel]
    );
  }, []);

  // Select all channels
  const selectAll = useCallback(() => {
    setSelectedChannels(allChannels);
  }, [allChannels]);

  // Clear all selections
  const clearAll = useCallback(() => {
    setSelectedChannels([]);
  }, []);

  // Select channels by criteria
  const selectByCondition = useCallback((
    condition: (channel: string) => boolean
  ) => {
    setSelectedChannels(allChannels.filter(condition));
  }, [allChannels]);

  // Batch update channels
  const updateChannels = useCallback((channels: string[]) => {
    setSelectedChannels(channels);
  }, []);

  // Check if channel is selected
  const isChannelSelected = useCallback((channel: string) => {
    return selectedChannels.includes(channel);
  }, [selectedChannels]);

  // Computed values
  const selectionCount = useMemo(() => selectedChannels.length, [selectedChannels]);
  const isAllSelected = useMemo(
    () => selectedChannels.length === allChannels.length,
    [selectedChannels, allChannels]
  );
  const isNoneSelected = useMemo(
    () => selectedChannels.length === 0,
    [selectedChannels]
  );

  return {
    selectedChannels,
    toggleChannel,
    selectAll,
    clearAll,
    selectByCondition,
    updateChannels,
    isChannelSelected,
    selectionCount,
    isAllSelected,
    isNoneSelected
  };
};