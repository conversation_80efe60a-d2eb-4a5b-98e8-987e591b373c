// HFO analysis operations hook
import { useState, useCallback, useMemo } from 'react';
import type { HFOEvent, HFOType } from '@/types/hfo';
import {
  calculateHfoStatsByType,
  filterHfoEventsByType,
  groupHfoEventsByChannel,
  countHfoPerChannel
} from '@/utils/hfoUtils';

interface UseHfoAnalysisOptions {
  hfoEvents: HFOEvent[];
  channels: string[];
  timeWindow?: [number, number];
}

export const useHfoAnalysis = ({
  hfoEvents,
  channels,
  timeWindow
}: UseHfoAnalysisOptions) => {
  const [visibleHfoTypes, setVisibleHfoTypes] = useState<HFOType[]>([
    'accepted',
    'rejected',
    'rejected_long',
    'lfo_rejected',
    'noise_rejected'
  ]);

  // Toggle HFO type visibility
  const toggleHfoType = useCallback((type: HFOType) => {
    setVisibleHfoTypes(prev =>
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  }, []);

  // Set all HFO types visible/hidden
  const setAllHfoTypesVisible = useCallback((visible: boolean) => {
    if (visible) {
      setVisibleHfoTypes(['accepted', 'rejected', 'rejected_long', 'lfo_rejected', 'noise_rejected']);
    } else {
      setVisibleHfoTypes([]);
    }
  }, []);

  // Computed values
  const hfoStatsByType = useMemo(
    () => calculateHfoStatsByType(hfoEvents),
    [hfoEvents]
  );

  const filteredHfoEvents = useMemo(
    () => filterHfoEventsByType(hfoEvents, visibleHfoTypes),
    [hfoEvents, visibleHfoTypes]
  );

  const hfoEventsByChannel = useMemo(
    () => groupHfoEventsByChannel(filteredHfoEvents, timeWindow),
    [filteredHfoEvents, timeWindow]
  );

  const hfoCountPerChannel = useMemo(
    () => countHfoPerChannel(hfoEvents, channels),
    [hfoEvents, channels]
  );

  const hfoEventsInWindow = useMemo(() => {
    if (!timeWindow) return filteredHfoEvents;
    return filteredHfoEvents.filter(
      event => event.start_time >= timeWindow[0] && event.start_time <= timeWindow[1]
    );
  }, [filteredHfoEvents, timeWindow]);

  const totalVisibleHfos = useMemo(
    () => filteredHfoEvents.length,
    [filteredHfoEvents]
  );

  const channelsWithHfos = useMemo(
    () => Object.keys(hfoCountPerChannel).filter(ch => hfoCountPerChannel[ch] > 0),
    [hfoCountPerChannel]
  );

  return {
    visibleHfoTypes,
    toggleHfoType,
    setAllHfoTypesVisible,
    setVisibleHfoTypes,
    hfoStatsByType,
    filteredHfoEvents,
    hfoEventsByChannel,
    hfoCountPerChannel,
    hfoEventsInWindow,
    totalVisibleHfos,
    channelsWithHfos
  };
};