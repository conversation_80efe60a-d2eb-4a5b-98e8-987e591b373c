// Keyboard shortcuts management hook
import { useEffect } from 'react';

interface KeyboardShortcut {
  key: string;
  handler: () => void;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  preventDefault?: boolean;
}

interface UseKeyboardShortcutsOptions {
  shortcuts: KeyboardShortcut[];
  enabled?: boolean;
  excludeInputElements?: boolean;
}

export const useKeyboardShortcuts = ({
  shortcuts,
  enabled = true,
  excludeInputElements = true
}: UseKeyboardShortcutsOptions) => {
  useEffect(() => {
    if (!enabled) return;

    const handleKeyPress = (e: KeyboardEvent) => {
      // Skip if focused on input elements
      if (excludeInputElements) {
        const target = e.target as HTMLElement;
        if (target instanceof HTMLInputElement ||
            target instanceof HTMLTextAreaElement ||
            target instanceof HTMLSelectElement) {
          return;
        }
      }

      // Find matching shortcut
      const shortcut = shortcuts.find(s => {
        const keyMatch = s.key.toLowerCase() === e.key.toLowerCase();
        const ctrlMatch = s.ctrlKey === undefined || s.ctrlKey === e.ctrlKey;
        const shiftMatch = s.shiftKey === undefined || s.shiftKey === e.shiftKey;
        const altMatch = s.altKey === undefined || s.altKey === e.altKey;
        const metaMatch = s.metaKey === undefined || s.metaKey === e.metaKey;

        return keyMatch && ctrlMatch && shiftMatch && altMatch && metaMatch;
      });

      if (shortcut) {
        if (shortcut.preventDefault !== false) {
          e.preventDefault();
        }
        shortcut.handler();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [shortcuts, enabled, excludeInputElements]);
};

// Common plot navigation shortcuts
export const usePlotNavigationShortcuts = ({
  onZoomIn,
  onZoomOut,
  onNavigatePrev,
  onNavigateNext,
  onFullscreen,
  onReset,
  enabled = true
}: {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onNavigatePrev: () => void;
  onNavigateNext: () => void;
  onFullscreen?: () => void;
  onReset?: () => void;
  enabled?: boolean;
}) => {
  const shortcuts: KeyboardShortcut[] = [
    { key: 'a', handler: onZoomIn },
    { key: 'd', handler: onZoomOut },
    { key: 'o', handler: onNavigatePrev },
    { key: 'p', handler: onNavigateNext },
    ...(onFullscreen ? [{ key: 'f', handler: onFullscreen }] : []),
    ...(onReset ? [{ key: 'r', handler: onReset }] : [])
  ];

  useKeyboardShortcuts({ shortcuts, enabled });
};