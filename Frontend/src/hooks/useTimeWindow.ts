// Time window management hook
import { useState, useCallback, useMemo } from 'react';

interface UseTimeWindowOptions {
  initialWindow?: [number, number];
  duration: number;
  defaultWindowSize?: number;
}

export const useTimeWindow = ({
  initialWindow,
  duration,
  defaultWindowSize = 10
}: UseTimeWindowOptions) => {
  const [timeWindow, setTimeWindow] = useState<[number, number]>(
    initialWindow || [0, Math.min(defaultWindowSize, duration)]
  );

  const windowSize = useMemo(() => timeWindow[1] - timeWindow[0], [timeWindow]);

  // Zoom operations
  const zoomIn = useCallback(() => {
    const center = (timeWindow[0] + timeWindow[1]) / 2;
    const newRange = windowSize * 0.75;
    setTimeWindow([
      Math.max(0, center - newRange / 2),
      Math.min(duration, center + newRange / 2)
    ]);
  }, [timeWindow, windowSize, duration]);

  const zoomOut = useCallback(() => {
    const center = (timeWindow[0] + timeWindow[1]) / 2;
    const newRange = Math.min(windowSize * 1.33, duration);
    setTimeWindow([
      Math.max(0, center - newRange / 2),
      Math.min(duration, center + newRange / 2)
    ]);
  }, [timeWindow, windowSize, duration]);

  // Navigation operations
  const navigate = useCallback((direction: 'prev' | 'next') => {
    const step = windowSize / 2;
    if (direction === 'prev') {
      const newStart = Math.max(0, timeWindow[0] - step);
      setTimeWindow([newStart, Math.min(newStart + windowSize, duration)]);
    } else {
      const newStart = Math.min(duration - windowSize, timeWindow[0] + step);
      setTimeWindow([newStart, Math.min(newStart + windowSize, duration)]);
    }
  }, [timeWindow, windowSize, duration]);

  // Set window to specific size
  const setWindowSize = useCallback((size: number) => {
    const center = (timeWindow[0] + timeWindow[1]) / 2;
    const newStart = Math.max(0, Math.min(duration - size, center - size / 2));
    setTimeWindow([newStart, newStart + size]);
  }, [timeWindow, duration]);

  // Reset to initial state
  const reset = useCallback(() => {
    setTimeWindow(initialWindow || [0, Math.min(defaultWindowSize, duration)]);
  }, [initialWindow, defaultWindowSize, duration]);

  // Jump to specific time
  const jumpToTime = useCallback((time: number) => {
    const halfWindow = windowSize / 2;
    const newStart = Math.max(0, Math.min(duration - windowSize, time - halfWindow));
    setTimeWindow([newStart, newStart + windowSize]);
  }, [windowSize, duration]);

  return {
    timeWindow,
    setTimeWindow,
    windowSize,
    zoomIn,
    zoomOut,
    navigate,
    setWindowSize,
    reset,
    jumpToTime,
    canNavigatePrev: timeWindow[0] > 0,
    canNavigateNext: timeWindow[1] < duration
  };
};