// Service for generating Plotly plot data
import * as Plotly from 'plotly.js';
import type { HFOEvent } from '@/types/hfo';
import {
  decimateData,
  calculateMaxPointsPerChannel,
  windowData,
  calculateChannelOffset,
  scaleDataWithGain
} from '@/utils/plotUtils';

interface PlotDataOptions {
  channelData: Record<string, number[]>;
  selectedChannels: string[];
  hfoEvents: HFOEvent[];
  timeWindow: [number, number];
  samplingRate: number;
  gain: number;
  showHFOMarkers: boolean;
  showThresholds: boolean;
}

export class PlotDataService {
  // Generate main EEG trace for a channel
  static generateEEGTrace(
    channel: string,
    data: number[],
    timeAxis: number[],
    index: number,
    channelOffset: number,
    gain: number
  ): Plotly.Data {
    const scaledData = scaleDataWithGain(data, gain);
    const offsetData = scaledData.map(val => val + index * channelOffset);

    return {
      x: timeAxis,
      y: offsetData,
      type: 'scattergl',
      mode: 'lines',
      name: channel,
      line: {
        color: '#1e293b',
        width: 1,
        simplify: false
      },
      hovertemplate: `${channel}<br>Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`,
    };
  }

  // Generate HFO markers and regions
  static generateHFOMarkers(
    hfoEvents: HFOEvent[],
    channel: string,
    index: number,
    channelOffset: number,
    timeWindow: [number, number]
  ): Plotly.Data[] {
    const traces: Plotly.Data[] = [];
    const channelHFOs = hfoEvents.filter(
      hfo => hfo.channel === channel &&
      hfo.start_time >= timeWindow[0] &&
      hfo.start_time <= timeWindow[1]
    );

    if (channelHFOs.length === 0) return traces;

    // HFO start markers
    traces.push({
      x: channelHFOs.map(hfo => hfo.start_time),
      y: channelHFOs.map(() => index * channelOffset),
      type: 'scattergl',
      mode: 'markers',
      name: `${channel} HFOs`,
      marker: {
        color: '#ef4444',
        size: 8,
        symbol: 'diamond',
      },
      showlegend: false,
      hovertemplate: 'HFO Event<br>Time: %{x:.2f}s<br>Peak Freq: %{customdata:.1f}Hz<extra></extra>',
      customdata: channelHFOs.map(hfo => hfo.peak_frequency),
    });

    // HFO duration bars and vertical lines
    channelHFOs.forEach(hfo => {
      // Duration bar
      traces.push({
        x: [hfo.start_time, hfo.end_time],
        y: [index * channelOffset - 10, index * channelOffset - 10],
        type: 'scattergl',
        mode: 'lines',
        line: {
          color: 'rgba(239, 68, 68, 0.6)',
          width: 4,
        },
        showlegend: false,
        hovertemplate: `HFO Duration<br>Start: ${hfo.start_time.toFixed(3)}s<br>End: ${hfo.end_time.toFixed(3)}s<br>Duration: ${((hfo.end_time - hfo.start_time) * 1000).toFixed(1)}ms<extra></extra>`,
      });

      // Vertical lines at start and end
      traces.push(
        {
          x: [hfo.start_time, hfo.start_time],
          y: [index * channelOffset - 20, index * channelOffset + 20],
          type: 'scattergl',
          mode: 'lines',
          line: { color: 'rgba(239, 68, 68, 0.3)', width: 1, dash: 'dot' },
          showlegend: false,
          hoverinfo: 'skip',
        },
        {
          x: [hfo.end_time, hfo.end_time],
          y: [index * channelOffset - 20, index * channelOffset + 20],
          type: 'scattergl',
          mode: 'lines',
          line: { color: 'rgba(239, 68, 68, 0.3)', width: 1, dash: 'dot' },
          showlegend: false,
          hoverinfo: 'skip',
        }
      );
    });

    return traces;
  }

  // Generate threshold lines
  static generateThresholdLines(
    channel: string,
    index: number,
    channelOffset: number,
    timeAxis: number[]
  ): Plotly.Data[] {
    const upperThreshold = index * channelOffset + 30;
    const lowerThreshold = index * channelOffset - 30;

    return [
      {
        x: timeAxis,
        y: new Array(timeAxis.length).fill(upperThreshold),
        type: 'scattergl',
        mode: 'lines',
        name: `${channel} upper threshold`,
        line: { color: 'rgba(59, 130, 246, 0.3)', width: 1, dash: 'dot' },
        showlegend: false,
        hovertemplate: 'Upper threshold<extra></extra>',
      },
      {
        x: timeAxis,
        y: new Array(timeAxis.length).fill(lowerThreshold),
        type: 'scattergl',
        mode: 'lines',
        name: `${channel} lower threshold`,
        line: { color: 'rgba(59, 130, 246, 0.3)', width: 1, dash: 'dot' },
        showlegend: false,
        hovertemplate: 'Lower threshold<extra></extra>',
      }
    ];
  }

  // Generate all plot data
  static generatePlotData(options: PlotDataOptions): Plotly.Data[] {
    const {
      channelData,
      selectedChannels,
      hfoEvents,
      timeWindow,
      samplingRate,
      gain,
      showHFOMarkers,
      showThresholds
    } = options;

    const traces: Plotly.Data[] = [];
    const windowDuration = timeWindow[1] - timeWindow[0];
    const maxPointsPerChannel = calculateMaxPointsPerChannel(windowDuration);
    const channelOffset = calculateChannelOffset(selectedChannels.length, gain);

    selectedChannels.forEach((channel, index) => {
      const data = channelData[channel];
      if (!data || data.length === 0) return;

      // Window and decimate data
      const { windowedData, windowedTime } = windowData(data, timeWindow, samplingRate);

      let processedData = windowedData;
      let processedTime = windowedTime;

      if (windowedData.length > maxPointsPerChannel) {
        processedData = decimateData(windowedData, maxPointsPerChannel);
        const decimationFactor = Math.ceil(windowedData.length / maxPointsPerChannel);
        processedTime = windowedTime.filter((_, i) => i % decimationFactor === 0);
      }

      // Main EEG trace
      traces.push(
        this.generateEEGTrace(channel, processedData, processedTime, index, channelOffset, gain)
      );

      // HFO markers
      if (showHFOMarkers) {
        traces.push(
          ...this.generateHFOMarkers(hfoEvents, channel, index, channelOffset, timeWindow)
        );
      }

      // Threshold lines
      if (showThresholds) {
        traces.push(
          ...this.generateThresholdLines(channel, index, channelOffset, processedTime)
        );
      }
    });

    return traces;
  }

  // Generate layout configuration
  static generateLayout(
    selectedChannels: string[],
    timeWindow: [number, number],
    channelOffset: number,
    height: number,
    metadata?: { filename?: string; montage?: string; frequency_band?: string }
  ): Partial<Plotly.Layout> {
    return {
      title: {
        text: metadata?.filename || 'HFO Analysis',
        font: {
          family: 'system-ui, -apple-system, sans-serif',
          size: 16,
          color: '#1e293b'
        }
      },
      xaxis: {
        title: {
          text: 'Time (seconds)',
          font: {
            family: 'system-ui, -apple-system, sans-serif',
            size: 12,
            color: '#64748b'
          }
        },
        range: timeWindow,
        gridcolor: '#e2e8f0',
        gridwidth: 0.5,
        linecolor: '#cbd5e1',
        linewidth: 1,
        tickfont: {
          family: 'system-ui, -apple-system, sans-serif',
          size: 10,
          color: '#64748b'
        },
        zeroline: false
      },
      yaxis: {
        title: {
          text: 'Channels',
          font: {
            family: 'system-ui, -apple-system, sans-serif',
            size: 12,
            color: '#64748b'
          }
        },
        tickmode: 'array' as const,
        tickvals: selectedChannels.map((_, i) => i * channelOffset),
        ticktext: selectedChannels,
        range: [selectedChannels.length * channelOffset + channelOffset / 2, -channelOffset / 2],
        autorange: false,
        gridcolor: '#f8fafc',
        gridwidth: 0.5,
        linecolor: '#cbd5e1',
        linewidth: 1,
        tickfont: {
          family: 'system-ui, -apple-system, sans-serif',
          size: 10,
          color: '#64748b'
        },
        zeroline: false
      },
      height: height,
      margin: { l: 100, r: 30, t: 50, b: 60 },
      hovermode: 'closest' as const,
      hoverlabel: {
        bgcolor: 'white',
        font: {
          family: 'system-ui, -apple-system, sans-serif',
          size: 11,
          color: '#1e293b'
        },
        bordercolor: '#e2e8f0'
      },
      showlegend: false,
      plot_bgcolor: '#ffffff',
      paper_bgcolor: '#ffffff',
      annotations: metadata ? [
        {
          text: `Montage: ${metadata.montage || 'N/A'} | Frequency: ${metadata.frequency_band || 'N/A'}`,
          showarrow: false,
          x: 0,
          y: 1.05,
          xref: 'paper' as const,
          yref: 'paper' as const,
          xanchor: 'left' as const,
          font: {
            family: 'system-ui, -apple-system, sans-serif',
            size: 10,
            color: '#64748b'
          }
        }
      ] : []
    };
  }

  // Generate plot config
  static generateConfig(metadata?: { filename?: string }): Partial<Plotly.Config> {
    return {
      displayModeBar: true,
      displaylogo: false,
      modeBarButtonsToRemove: ['lasso2d' as const, 'select2d' as const, 'autoScale2d' as const],
      modeBarButtonsToAdd: [],
      toImageButtonOptions: {
        format: 'png' as const,
        filename: `hfo_analysis_${metadata?.filename?.replace('.edf', '') || 'export'}_${new Date().toISOString().split('T')[0]}`,
        height: 1200,
        width: 1920,
        scale: 2,
      },
      responsive: true,
      scrollZoom: true,
      doubleClick: 'reset' as const,
      showTips: false,
      plotGlPixelRatio: 1,
    };
  }
}