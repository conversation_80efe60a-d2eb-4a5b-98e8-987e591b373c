// Download utilities for results export
import type { AnalysisResults } from '@/hooks/useAnalysisResults';
import { convertHfoEventsToCSV } from '@/utils/hfoUtils';

export const downloadFile = (
  data: string,
  filename: string,
  mimeType: string = 'text/plain'
) => {
  const blob = new Blob([data], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

export const downloadResultsAsJSON = (
  results: AnalysisResults,
  jobId: string
) => {
  const data = JSON.stringify(results, null, 2);
  downloadFile(data, `analysis_results_${jobId}.json`, 'application/json');
};

export const downloadResultsAsCSV = (
  results: AnalysisResults,
  jobId: string
) => {
  const csv = convertHfoEventsToCSV(results.hfo_events);
  downloadFile(csv, `hfo_events_${jobId}.csv`, 'text/csv');
};

export const generateComprehensiveReport = (
  results: AnalysisResults
): string => {
  const report = [
    'HFO Analysis Comprehensive Report',
    '=================================',
    '',
    'File Information:',
    `-----------------`,
    `Filename: ${results.metadata.filename}`,
    `Duration: ${results.metadata.duration_seconds}s`,
    `Sampling Rate: ${results.metadata.sampling_rate}Hz`,
    `Number of Channels: ${results.metadata.channels.length}`,
    `Processing Time: ${results.metadata.processing_time}s`,
    '',
    'Analysis Parameters:',
    `-------------------`,
    `Montage: ${results.metadata.montage || 'Bipolar'}`,
    `Frequency Band: ${results.metadata.low_cutoff || 80}-${results.metadata.high_cutoff || 500} Hz`,
    '',
    'HFO Statistics:',
    `---------------`,
    `Total HFOs Detected: ${results.statistics.total_hfos}`,
    `HFO Density: ${results.statistics.hfo_density.toFixed(2)} HFOs/min`,
    `Channels with HFOs: ${results.statistics.channels_with_hfos.length}`,
    '',
    'Channel-wise HFO Distribution:',
    `------------------------------`
  ];

  if (results.statistics.hfo_rate_per_channel) {
    Object.entries(results.statistics.hfo_rate_per_channel)
      .sort((a, b) => b[1] - a[1])
      .forEach(([channel, count]) => {
        report.push(`${channel}: ${count} HFOs`);
      });
  }

  report.push('', 'HFO Events Detail:', '-----------------', '');
  report.push(convertHfoEventsToCSV(results.hfo_events));

  return report.join('\n');
};