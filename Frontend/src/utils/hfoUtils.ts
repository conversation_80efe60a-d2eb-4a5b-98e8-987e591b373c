// HFO event utilities for processing and visualization
import type { HFOEvent, HFOType } from '@/types/hfo';
import { HFO_TYPE_COLORS } from '@/types/hfo';

// Calculate HFO statistics by type
export const calculateHfoStatsByType = (hfoEvents: HFOEvent[]) => {
  return {
    total: hfoEvents.length,
    accepted: hfoEvents.filter(e => (e.type || 'accepted') === 'accepted').length,
    rejected: hfoEvents.filter(e => e.type === 'rejected').length,
    rejected_long: hfoEvents.filter(e => e.type === 'rejected_long').length,
    lfo_rejected: hfoEvents.filter(e => e.type === 'lfo_rejected').length,
    noise_rejected: hfoEvents.filter(e => e.type === 'noise_rejected').length,
  };
};

// Filter HFO events by visible types
export const filterHfoEventsByType = (
  hfoEvents: HFOEvent[],
  visibleTypes: HFOType[]
): HFOEvent[] => {
  return hfoEvents.filter(event => {
    const eventType = event.type || 'accepted';
    return visibleTypes.includes(eventType);
  });
};

// Group HFO events by channel
export const groupHfoEventsByChannel = (
  hfoEvents: HFOEvent[],
  timeWindow?: [number, number]
): Record<string, HFOEvent[]> => {
  const eventsByChannel: Record<string, HFOEvent[]> = {};

  hfoEvents.forEach(event => {
    // Filter by time window if specified
    if (timeWindow && (event.start_time < timeWindow[0] || event.start_time > timeWindow[1])) {
      return;
    }

    if (!eventsByChannel[event.channel]) {
      eventsByChannel[event.channel] = [];
    }
    eventsByChannel[event.channel].push(event);
  });

  return eventsByChannel;
};

// Count HFO events per channel
export const countHfoPerChannel = (
  hfoEvents: HFOEvent[],
  channels: string[]
): Record<string, number> => {
  const counts: Record<string, number> = {};
  channels.forEach(channel => {
    counts[channel] = hfoEvents.filter(e => e.channel === channel).length;
  });
  return counts;
};

// Get HFO events within time window
export const getHfoEventsInWindow = (
  hfoEvents: HFOEvent[],
  channel: string,
  timeWindow: [number, number]
): HFOEvent[] => {
  return hfoEvents.filter(
    hfo => hfo.channel === channel &&
    hfo.start_time >= timeWindow[0] &&
    hfo.start_time <= timeWindow[1]
  );
};

// Format HFO event for display
export const formatHfoEvent = (event: HFOEvent) => {
  const duration = (event.end_time - event.start_time) * 1000;
  return {
    channel: event.channel,
    startTime: event.start_time.toFixed(3),
    endTime: event.end_time.toFixed(3),
    duration: duration.toFixed(1),
    frequency: event.peak_frequency.toFixed(1),
    amplitude: event.amplitude.toFixed(2),
    type: event.type || 'accepted',
    color: HFO_TYPE_COLORS[event.type || 'accepted']
  };
};

// Convert HFO events to CSV format
export const convertHfoEventsToCSV = (events: HFOEvent[]): string => {
  const headers = ["Channel", "Start Time", "End Time", "Peak Frequency", "Amplitude", "Type"];
  const rows = events.map(e => [
    e.channel,
    e.start_time,
    e.end_time,
    e.peak_frequency,
    e.amplitude,
    e.type || 'accepted'
  ]);
  return [headers, ...rows].map(row => row.join(",")).join("\n");
};