// Plot data generation and manipulation utilities

// Intelligent data decimation for performance optimization
export const decimateData = (data: number[], maxPoints: number): number[] => {
  if (data.length <= maxPoints) return data;

  const step = Math.ceil(data.length / maxPoints);
  const decimated: number[] = [];

  for (let i = 0; i < data.length; i += step) {
    // Use max value in window to preserve peaks
    const windowEnd = Math.min(i + step, data.length);
    let maxVal = data[i];
    for (let j = i + 1; j < windowEnd; j++) {
      if (Math.abs(data[j]) > Math.abs(maxVal)) {
        maxVal = data[j];
      }
    }
    decimated.push(maxVal);
  }

  return decimated;
};

// Calculate optimal decimation level based on window size
export const calculateMaxPointsPerChannel = (windowDuration: number): number => {
  if (windowDuration <= 10) {
    return 50000;  // Full resolution for < 10 seconds
  } else if (windowDuration <= 60) {
    return 20000;  // Medium resolution for < 1 minute
  } else if (windowDuration <= 300) {
    return 10000;  // Lower resolution for < 5 minutes
  } else {
    return 5000;   // Minimum resolution for full recording
  }
};

// Window data for a specific time range
export const windowData = (
  data: number[],
  timeWindow: [number, number],
  samplingRate: number
): { windowedData: number[], windowedTime: number[] } => {
  const timeAxis = Array.from({ length: data.length }, (_, i) => i / samplingRate);
  const startIdx = Math.floor(timeWindow[0] * samplingRate);
  const endIdx = Math.ceil(timeWindow[1] * samplingRate);

  return {
    windowedData: data.slice(startIdx, endIdx),
    windowedTime: timeAxis.slice(startIdx, endIdx)
  };
};

// Calculate channel offset for multi-channel display
export const calculateChannelOffset = (
  channelCount: number,
  gain: number = 20
): number => {
  const baseOffset = Math.max(100, 800 / Math.max(channelCount, 1));
  return baseOffset / (gain / 20);
};

// Scale data with gain
export const scaleDataWithGain = (
  data: number[],
  gain: number
): number[] => {
  return data.map(val => (val * 20 / gain));
};

// Calculate data range and statistics
export const calculateDataStats = (data: number[]): {
  min: number;
  max: number;
  range: number;
  mean: number;
  std: number;
} => {
  if (data.length === 0) {
    return { min: 0, max: 0, range: 0, mean: 0, std: 0 };
  }

  const min = Math.min(...data);
  const max = Math.max(...data);
  const range = max - min || 1;

  const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
  const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length;
  const std = Math.sqrt(variance);

  return { min, max, range, mean, std };
};

// Generate time axis for data
export const generateTimeAxis = (
  dataLength: number,
  samplingRate: number
): number[] => {
  return Array.from({ length: dataLength }, (_, i) => i / samplingRate);
};