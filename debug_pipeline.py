#!/usr/bin/env python3
"""
Diagnostic script to troubleshoot HFO processing pipeline issues.
Checks SQS queue status, DynamoDB jobs, and ECS service health.
"""

import json
import os
import sys
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional

import boto3
from botocore.exceptions import ClientError, NoCredentialsError


class PipelineDiagnostics:
    """Diagnostic tools for HFO processing pipeline"""
    
    def __init__(self, profile: str = "biormika"):
        """Initialize with AWS profile"""
        self.profile = profile
        self.session = boto3.Session(profile_name=profile)
        
        # Initialize clients
        self.sqs = self.session.client("sqs")
        self.dynamodb = self.session.resource("dynamodb")
        self.ecs = self.session.client("ecs")
        self.cloudwatch = self.session.client("cloudwatch")
        
        # Resource names (update these based on your deployment)
        self.queue_name = "biormika-analysis-jobs"
        self.dlq_name = "biormika-analysis-jobs-dlq"
        self.jobs_table_name = "biormika-analysis-jobs"
        self.cluster_name = "BiormikaHFOCluster"
        self.service_name = "HFOProcessorService"
        
    def check_sqs_queue_status(self) -> Dict:
        """Check SQS queue configuration and current status"""
        print("🔍 Checking SQS Queue Status...")
        
        try:
            # Get queue URL
            queue_url = self.sqs.get_queue_url(QueueName=self.queue_name)["QueueUrl"]
            print(f"✅ Queue URL: {queue_url}")
            
            # Get queue attributes
            attributes = self.sqs.get_queue_attributes(
                QueueUrl=queue_url,
                AttributeNames=[
                    "ApproximateNumberOfMessages",
                    "ApproximateNumberOfMessagesNotVisible", 
                    "ApproximateNumberOfMessagesDelayed",
                    "VisibilityTimeout",
                    "MessageRetentionPeriod",
                    "ReceiveMessageWaitTimeSeconds",
                    "RedrivePolicy"
                ]
            )["Attributes"]
            
            print(f"📊 Queue Metrics:")
            print(f"   - Messages Available: {attributes.get('ApproximateNumberOfMessages', 0)}")
            print(f"   - Messages In Flight: {attributes.get('ApproximateNumberOfMessagesNotVisible', 0)}")
            print(f"   - Messages Delayed: {attributes.get('ApproximateNumberOfMessagesDelayed', 0)}")
            print(f"   - Visibility Timeout: {attributes.get('VisibilityTimeout', 'N/A')} seconds")
            print(f"   - Message Retention: {attributes.get('MessageRetentionPeriod', 'N/A')} seconds")
            print(f"   - Long Polling: {attributes.get('ReceiveMessageWaitTimeSeconds', 'N/A')} seconds")
            
            # Check DLQ configuration
            redrive_policy = attributes.get("RedrivePolicy")
            if redrive_policy:
                policy = json.loads(redrive_policy)
                print(f"   - DLQ Max Receives: {policy.get('maxReceiveCount', 'N/A')}")
                print(f"   - DLQ ARN: {policy.get('deadLetterTargetArn', 'N/A')}")
            
            return {
                "queue_url": queue_url,
                "attributes": attributes,
                "status": "healthy"
            }
            
        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "AWS.SimpleQueueService.NonExistentQueue":
                print(f"❌ Queue '{self.queue_name}' does not exist!")
                return {"status": "queue_not_found", "error": str(e)}
            else:
                print(f"❌ Error accessing queue: {e}")
                return {"status": "error", "error": str(e)}
    
    def check_dlq_status(self) -> Dict:
        """Check Dead Letter Queue status"""
        print("\n🔍 Checking Dead Letter Queue...")
        
        try:
            dlq_url = self.sqs.get_queue_url(QueueName=self.dlq_name)["QueueUrl"]
            print(f"✅ DLQ URL: {dlq_url}")
            
            attributes = self.sqs.get_queue_attributes(
                QueueUrl=dlq_url,
                AttributeNames=["ApproximateNumberOfMessages"]
            )["Attributes"]
            
            dlq_messages = int(attributes.get("ApproximateNumberOfMessages", 0))
            print(f"📊 DLQ Messages: {dlq_messages}")
            
            if dlq_messages > 0:
                print(f"⚠️  WARNING: {dlq_messages} messages in DLQ - indicates processing failures!")
                
                # Sample a few messages from DLQ
                messages = self.sqs.receive_message(
                    QueueUrl=dlq_url,
                    MaxNumberOfMessages=min(3, dlq_messages),
                    WaitTimeSeconds=1
                )
                
                if "Messages" in messages:
                    print("📋 Sample DLQ Messages:")
                    for i, msg in enumerate(messages["Messages"][:3]):
                        try:
                            body = json.loads(msg["Body"])
                            print(f"   {i+1}. Job ID: {body.get('job_id', 'N/A')}")
                            print(f"      File: {body.get('file_key', 'N/A')}")
                        except json.JSONDecodeError:
                            print(f"   {i+1}. Raw message: {msg['Body'][:100]}...")
            
            return {"dlq_url": dlq_url, "message_count": dlq_messages, "status": "healthy"}
            
        except ClientError as e:
            print(f"❌ Error accessing DLQ: {e}")
            return {"status": "error", "error": str(e)}
    
    def check_dynamodb_jobs(self, hours_back: int = 24) -> Dict:
        """Check recent jobs in DynamoDB"""
        print(f"\n🔍 Checking DynamoDB Jobs (last {hours_back} hours)...")
        
        try:
            table = self.dynamodb.Table(self.jobs_table_name)
            
            # Get jobs from the last N hours
            cutoff_time = (datetime.utcnow() - timedelta(hours=hours_back)).isoformat()
            
            # Scan for recent jobs (in production, use GSI for better performance)
            response = table.scan(
                FilterExpression="created_at > :cutoff",
                ExpressionAttributeValues={":cutoff": cutoff_time},
                Limit=50
            )
            
            jobs = response.get("Items", [])
            print(f"📊 Found {len(jobs)} jobs in last {hours_back} hours")
            
            # Analyze job statuses
            status_counts = {}
            pending_jobs = []
            
            for job in jobs:
                status = job.get("status", "unknown")
                status_counts[status] = status_counts.get(status, 0) + 1
                
                if status == "pending":
                    pending_jobs.append({
                        "job_id": job.get("job_id"),
                        "file_key": job.get("file_key"),
                        "created_at": job.get("created_at"),
                        "user_email": job.get("user_email")
                    })
            
            print("📈 Job Status Summary:")
            for status, count in status_counts.items():
                print(f"   - {status}: {count}")
            
            if pending_jobs:
                print(f"\n⚠️  {len(pending_jobs)} jobs stuck in pending state:")
                for job in pending_jobs[:5]:  # Show first 5
                    print(f"   - {job['job_id']}: {job['file_key']} (created: {job['created_at']})")
                if len(pending_jobs) > 5:
                    print(f"   ... and {len(pending_jobs) - 5} more")
            
            return {
                "total_jobs": len(jobs),
                "status_counts": status_counts,
                "pending_jobs": pending_jobs,
                "status": "healthy"
            }
            
        except ClientError as e:
            print(f"❌ Error accessing DynamoDB: {e}")
            return {"status": "error", "error": str(e)}
    
    def check_ecs_service(self) -> Dict:
        """Check ECS Fargate service status"""
        print(f"\n🔍 Checking ECS Service Status...")
        
        try:
            # Get service details
            services = self.ecs.describe_services(
                cluster=self.cluster_name,
                services=[self.service_name]
            )
            
            if not services["services"]:
                print(f"❌ Service '{self.service_name}' not found in cluster '{self.cluster_name}'")
                return {"status": "service_not_found"}
            
            service = services["services"][0]
            
            print(f"✅ Service: {service['serviceName']}")
            print(f"📊 Service Status: {service['status']}")
            print(f"   - Desired Count: {service['desiredCount']}")
            print(f"   - Running Count: {service['runningCount']}")
            print(f"   - Pending Count: {service['pendingCount']}")
            
            # Check task definition
            task_def_arn = service["taskDefinition"]
            print(f"   - Task Definition: {task_def_arn.split('/')[-1]}")
            
            # Get running tasks
            tasks = self.ecs.list_tasks(
                cluster=self.cluster_name,
                serviceName=self.service_name
            )
            
            print(f"   - Active Tasks: {len(tasks['taskArns'])}")
            
            if tasks["taskArns"]:
                # Get task details
                task_details = self.ecs.describe_tasks(
                    cluster=self.cluster_name,
                    tasks=tasks["taskArns"]
                )
                
                print("📋 Task Details:")
                for task in task_details["tasks"]:
                    print(f"   - {task['taskArn'].split('/')[-1]}: {task['lastStatus']}")
                    if task.get("stoppedReason"):
                        print(f"     Stopped Reason: {task['stoppedReason']}")
            
            return {
                "service_status": service["status"],
                "desired_count": service["desiredCount"],
                "running_count": service["runningCount"],
                "pending_count": service["pendingCount"],
                "task_count": len(tasks["taskArns"]),
                "status": "healthy"
            }
            
        except ClientError as e:
            print(f"❌ Error accessing ECS: {e}")
            return {"status": "error", "error": str(e)}
    
    def run_full_diagnostic(self) -> Dict:
        """Run complete pipeline diagnostic"""
        print("🚀 Starting HFO Pipeline Diagnostics...\n")
        
        results = {
            "timestamp": datetime.utcnow().isoformat(),
            "sqs_queue": self.check_sqs_queue_status(),
            "dlq": self.check_dlq_status(),
            "dynamodb": self.check_dynamodb_jobs(),
            "ecs_service": self.check_ecs_service()
        }
        
        print("\n" + "="*60)
        print("📋 DIAGNOSTIC SUMMARY")
        print("="*60)
        
        # Overall health assessment
        issues = []
        
        if results["sqs_queue"]["status"] != "healthy":
            issues.append("SQS Queue issues detected")
        
        if results["dlq"].get("message_count", 0) > 0:
            issues.append(f"DLQ has {results['dlq']['message_count']} failed messages")
        
        if results["dynamodb"].get("pending_jobs"):
            issues.append(f"{len(results['dynamodb']['pending_jobs'])} jobs stuck pending")
        
        if results["ecs_service"].get("running_count", 0) == 0:
            issues.append("No ECS tasks running")
        
        if issues:
            print("❌ ISSUES FOUND:")
            for issue in issues:
                print(f"   - {issue}")
        else:
            print("✅ No major issues detected")
        
        return results


def main():
    """Main entry point"""
    try:
        diagnostics = PipelineDiagnostics()
        results = diagnostics.run_full_diagnostic()
        
        # Save results to file
        with open("pipeline_diagnostic_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: pipeline_diagnostic_results.json")
        
    except NoCredentialsError:
        print("❌ AWS credentials not configured. Please run 'aws configure --profile biormika'")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
