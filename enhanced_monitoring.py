#!/usr/bin/env python3
"""
Enhanced monitoring and alerting for HFO processing pipeline.
Provides real-time monitoring, automated health checks, and alerting.
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List

import boto3
from botocore.exceptions import ClientError


class PipelineMonitor:
    """Enhanced monitoring for HFO processing pipeline"""
    
    def __init__(self, profile: str = "biormika"):
        """Initialize monitoring with AWS profile"""
        self.profile = profile
        self.session = boto3.Session(profile_name=profile)
        
        # Initialize clients
        self.sqs = self.session.client("sqs")
        self.dynamodb = self.session.resource("dynamodb")
        self.ecs = self.session.client("ecs")
        self.cloudwatch = self.session.client("cloudwatch")
        
        # Resource names
        self.queue_name = "biormika-analysis-jobs"
        self.dlq_name = "biormika-analysis-jobs-dlq"
        self.jobs_table_name = "biormika-analysis-jobs"
        self.cluster_name = "biormika-hfo-cluster"
        self.service_name = "biormika-hfo-processor"
        
        # Get resource references
        self.queue_url = self.sqs.get_queue_url(QueueName=self.queue_name)["QueueUrl"]
        self.dlq_url = self.sqs.get_queue_url(QueueName=self.dlq_name)["QueueUrl"]
        self.jobs_table = self.dynamodb.Table(self.jobs_table_name)
    
    def get_pipeline_health(self) -> Dict:
        """Get comprehensive pipeline health status"""
        health = {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "healthy",
            "components": {}
        }
        
        # Check SQS queues
        try:
            main_queue_attrs = self.sqs.get_queue_attributes(
                QueueUrl=self.queue_url,
                AttributeNames=["ApproximateNumberOfMessages", "ApproximateNumberOfMessagesNotVisible"]
            )["Attributes"]
            
            dlq_attrs = self.sqs.get_queue_attributes(
                QueueUrl=self.dlq_url,
                AttributeNames=["ApproximateNumberOfMessages"]
            )["Attributes"]
            
            queue_health = {
                "status": "healthy",
                "main_queue_messages": int(main_queue_attrs.get("ApproximateNumberOfMessages", 0)),
                "main_queue_in_flight": int(main_queue_attrs.get("ApproximateNumberOfMessagesNotVisible", 0)),
                "dlq_messages": int(dlq_attrs.get("ApproximateNumberOfMessages", 0))
            }
            
            # Alert if DLQ has messages
            if queue_health["dlq_messages"] > 0:
                queue_health["status"] = "warning"
                queue_health["alert"] = f"DLQ has {queue_health['dlq_messages']} failed messages"
                health["overall_status"] = "warning"
            
            health["components"]["sqs"] = queue_health
            
        except Exception as e:
            health["components"]["sqs"] = {"status": "error", "error": str(e)}
            health["overall_status"] = "error"
        
        # Check ECS service
        try:
            services = self.ecs.describe_services(
                cluster=self.cluster_name,
                services=[self.service_name]
            )["services"]
            
            if services:
                service = services[0]
                ecs_health = {
                    "status": "healthy",
                    "service_status": service["status"],
                    "desired_count": service["desiredCount"],
                    "running_count": service["runningCount"],
                    "pending_count": service["pendingCount"]
                }
                
                # Alert if service is not active
                if service["status"] != "ACTIVE":
                    ecs_health["status"] = "error"
                    ecs_health["alert"] = f"Service status is {service['status']}"
                    health["overall_status"] = "error"
                
                health["components"]["ecs"] = ecs_health
            else:
                health["components"]["ecs"] = {"status": "error", "error": "Service not found"}
                health["overall_status"] = "error"
                
        except Exception as e:
            health["components"]["ecs"] = {"status": "error", "error": str(e)}
            health["overall_status"] = "error"
        
        # Check recent job status
        try:
            # Get jobs from last hour
            cutoff_time = (datetime.utcnow() - timedelta(hours=1)).isoformat()
            
            response = self.jobs_table.scan(
                FilterExpression="created_at > :cutoff",
                ExpressionAttributeValues={":cutoff": cutoff_time},
                Limit=100
            )
            
            recent_jobs = response.get("Items", [])
            
            # Analyze job statuses
            status_counts = {}
            stuck_jobs = []
            
            for job in recent_jobs:
                status = job.get("status", "unknown")
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # Check for jobs stuck in pending for > 30 minutes
                if status == "pending":
                    created_at = datetime.fromisoformat(job["created_at"])
                    age_minutes = (datetime.utcnow() - created_at).total_seconds() / 60
                    
                    if age_minutes > 30:
                        stuck_jobs.append({
                            "job_id": job["job_id"],
                            "age_minutes": int(age_minutes),
                            "file_key": job.get("file_key", "unknown")
                        })
            
            jobs_health = {
                "status": "healthy",
                "recent_jobs_count": len(recent_jobs),
                "status_distribution": status_counts,
                "stuck_jobs_count": len(stuck_jobs)
            }
            
            if stuck_jobs:
                jobs_health["status"] = "warning"
                jobs_health["alert"] = f"{len(stuck_jobs)} jobs stuck in pending > 30 minutes"
                jobs_health["stuck_jobs"] = stuck_jobs[:5]  # Show first 5
                health["overall_status"] = "warning"
            
            health["components"]["jobs"] = jobs_health
            
        except Exception as e:
            health["components"]["jobs"] = {"status": "error", "error": str(e)}
            health["overall_status"] = "error"
        
        return health
    
    def monitor_test_job(self, job_id: str, timeout_minutes: int = 10) -> Dict:
        """Monitor a specific test job until completion or timeout"""
        print(f"🔍 Monitoring test job: {job_id}")
        
        start_time = datetime.utcnow()
        timeout = timedelta(minutes=timeout_minutes)
        
        while datetime.utcnow() - start_time < timeout:
            try:
                # Check job status in DynamoDB
                response = self.jobs_table.get_item(Key={"job_id": job_id})
                
                if "Item" in response:
                    job = response["Item"]
                    status = job.get("status", "unknown")
                    
                    print(f"   Status: {status} (elapsed: {(datetime.utcnow() - start_time).total_seconds():.0f}s)")
                    
                    if status in ["completed", "failed"]:
                        return {
                            "job_id": job_id,
                            "final_status": status,
                            "elapsed_seconds": (datetime.utcnow() - start_time).total_seconds(),
                            "job_data": job
                        }
                else:
                    print(f"   Job not found in DynamoDB yet...")
                
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                print(f"   Error checking job: {e}")
                time.sleep(10)
        
        print(f"   ⏰ Timeout reached after {timeout_minutes} minutes")
        return {
            "job_id": job_id,
            "final_status": "timeout",
            "elapsed_seconds": timeout.total_seconds()
        }
    
    def create_custom_metrics(self) -> Dict:
        """Create custom CloudWatch metrics for better monitoring"""
        print("📊 Creating custom CloudWatch metrics...")
        
        try:
            health = self.get_pipeline_health()
            
            # Put custom metrics
            metrics = []
            
            # Queue metrics
            if "sqs" in health["components"]:
                sqs_data = health["components"]["sqs"]
                
                metrics.extend([
                    {
                        "MetricName": "MainQueueMessages",
                        "Value": sqs_data.get("main_queue_messages", 0),
                        "Unit": "Count"
                    },
                    {
                        "MetricName": "DLQMessages", 
                        "Value": sqs_data.get("dlq_messages", 0),
                        "Unit": "Count"
                    }
                ])
            
            # Job metrics
            if "jobs" in health["components"]:
                jobs_data = health["components"]["jobs"]
                
                metrics.extend([
                    {
                        "MetricName": "StuckJobs",
                        "Value": jobs_data.get("stuck_jobs_count", 0),
                        "Unit": "Count"
                    },
                    {
                        "MetricName": "RecentJobs",
                        "Value": jobs_data.get("recent_jobs_count", 0),
                        "Unit": "Count"
                    }
                ])
            
            # ECS metrics
            if "ecs" in health["components"]:
                ecs_data = health["components"]["ecs"]
                
                metrics.append({
                    "MetricName": "RunningTasks",
                    "Value": ecs_data.get("running_count", 0),
                    "Unit": "Count"
                })
            
            # Send metrics to CloudWatch
            for metric in metrics:
                self.cloudwatch.put_metric_data(
                    Namespace="Biormika/HFO",
                    MetricData=[{
                        "MetricName": metric["MetricName"],
                        "Value": metric["Value"],
                        "Unit": metric["Unit"],
                        "Timestamp": datetime.utcnow()
                    }]
                )
            
            print(f"✅ Created {len(metrics)} custom metrics")
            return {"metrics_created": len(metrics), "metrics": metrics}
            
        except Exception as e:
            print(f"❌ Error creating metrics: {e}")
            return {"error": str(e)}
    
    def run_health_check(self) -> Dict:
        """Run comprehensive health check and return results"""
        print("🏥 Running Pipeline Health Check...\n")
        
        health = self.get_pipeline_health()
        
        # Display results
        print(f"Overall Status: {health['overall_status'].upper()}")
        print(f"Timestamp: {health['timestamp']}")
        print()
        
        for component, data in health["components"].items():
            status = data.get("status", "unknown")
            print(f"{component.upper()}: {status.upper()}")
            
            if "alert" in data:
                print(f"  ⚠️  {data['alert']}")
            
            if "error" in data:
                print(f"  ❌ {data['error']}")
            
            # Component-specific details
            if component == "sqs":
                print(f"  Main Queue: {data.get('main_queue_messages', 0)} messages")
                print(f"  In Flight: {data.get('main_queue_in_flight', 0)} messages")
                print(f"  DLQ: {data.get('dlq_messages', 0)} messages")
            
            elif component == "ecs":
                print(f"  Service: {data.get('service_status', 'unknown')}")
                print(f"  Tasks: {data.get('running_count', 0)}/{data.get('desired_count', 0)} running")
            
            elif component == "jobs":
                print(f"  Recent Jobs: {data.get('recent_jobs_count', 0)}")
                print(f"  Stuck Jobs: {data.get('stuck_jobs_count', 0)}")
                
                if data.get("stuck_jobs"):
                    print("  Stuck Job Details:")
                    for job in data["stuck_jobs"][:3]:
                        print(f"    - {job['job_id']} ({job['age_minutes']}min old)")
            
            print()
        
        return health


def main():
    """Main entry point"""
    try:
        monitor = PipelineMonitor()
        
        # Run health check
        health = monitor.run_health_check()
        
        # Create custom metrics
        metrics_result = monitor.create_custom_metrics()
        
        # Save results
        results = {
            "health_check": health,
            "metrics": metrics_result
        }
        
        with open("pipeline_monitoring_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        print("💾 Results saved to: pipeline_monitoring_results.json")
        
        # If there's a test job, monitor it
        try:
            with open("pipeline_fix_results.json", "r") as f:
                fix_results = json.load(f)
                test_job_id = fix_results.get("steps", {}).get("test_submission", {}).get("test_job_id")
                
                if test_job_id:
                    print(f"\n🔍 Monitoring test job: {test_job_id}")
                    job_result = monitor.monitor_test_job(test_job_id, timeout_minutes=5)
                    print(f"📋 Test job result: {job_result['final_status']}")
                    
                    results["test_job_monitoring"] = job_result
                    
                    # Update results file
                    with open("pipeline_monitoring_results.json", "w") as f:
                        json.dump(results, f, indent=2, default=str)
        except FileNotFoundError:
            pass
        
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
