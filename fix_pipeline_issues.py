#!/usr/bin/env python3
"""
Fix HFO processing pipeline issues.
Addresses DynamoDB job status update failures and tests end-to-end flow.
"""

import json
import os
import sys
from datetime import datetime
from typing import Dict, List

import boto3
from botocore.exceptions import ClientError, NoCredentialsError


class PipelineFixer:
    """Tools to fix and test HFO processing pipeline"""
    
    def __init__(self, profile: str = "biormika"):
        """Initialize with AWS profile"""
        self.profile = profile
        self.session = boto3.Session(profile_name=profile)
        
        # Initialize clients
        self.sqs = self.session.client("sqs")
        self.dynamodb = self.session.resource("dynamodb")
        self.ecs = self.session.client("ecs")
        
        # Resource names
        self.queue_name = "biormika-analysis-jobs"
        self.dlq_name = "biormika-analysis-jobs-dlq"
        self.jobs_table_name = "biormika-analysis-jobs"
        self.cluster_name = "biormika-hfo-cluster"
        self.service_name = "biormika-hfo-processor"
        
        # Get resource references
        self.queue_url = self.sqs.get_queue_url(QueueName=self.queue_name)["QueueUrl"]
        self.dlq_url = self.sqs.get_queue_url(QueueName=self.dlq_name)["QueueUrl"]
        self.jobs_table = self.dynamodb.Table(self.jobs_table_name)
    
    def fix_pending_jobs(self) -> Dict:
        """Fix jobs stuck in pending state by checking their actual status"""
        print("🔧 Fixing jobs stuck in pending state...")
        
        try:
            # Get all pending jobs
            response = self.jobs_table.scan(
                FilterExpression="attribute_exists(job_id) AND #status = :status",
                ExpressionAttributeNames={"#status": "status"},
                ExpressionAttributeValues={":status": "pending"}
            )
            
            pending_jobs = response.get("Items", [])
            print(f"📊 Found {len(pending_jobs)} pending jobs")
            
            fixed_jobs = []
            
            for job in pending_jobs:
                job_id = job["job_id"]
                file_key = job["file_key"]
                
                # Check if results exist in S3 (indicates job was actually completed)
                s3 = self.session.client("s3")
                bucket_name = "biormikastack-s3storageedfstoragebucket8b55a415-vt2ocke56liv"  # Update with your bucket
                results_key = f"results/{job_id}/analysis_results.json"
                
                try:
                    s3.head_object(Bucket=bucket_name, Key=results_key)
                    
                    # Results exist - job was completed but status not updated
                    print(f"✅ Found results for job {job_id}, updating status to completed")
                    
                    # Update job status to completed
                    self.jobs_table.update_item(
                        Key={"job_id": job_id},
                        UpdateExpression="SET #status = :status, updated_at = :updated, completed_at = :completed, results_url = :url",
                        ExpressionAttributeNames={"#status": "status"},
                        ExpressionAttributeValues={
                            ":status": "completed",
                            ":updated": datetime.utcnow().isoformat(),
                            ":completed": datetime.utcnow().isoformat(),
                            ":url": f"s3://{bucket_name}/{results_key}"
                        }
                    )
                    
                    fixed_jobs.append({
                        "job_id": job_id,
                        "file_key": file_key,
                        "action": "marked_completed"
                    })
                    
                except ClientError as e:
                    if e.response["Error"]["Code"] == "404":
                        # No results found - job may have actually failed
                        print(f"⚠️  No results found for job {job_id}, checking age...")
                        
                        # If job is older than 1 hour, mark as failed
                        created_at = datetime.fromisoformat(job["created_at"])
                        age_hours = (datetime.utcnow() - created_at).total_seconds() / 3600
                        
                        if age_hours > 1:
                            print(f"❌ Job {job_id} is {age_hours:.1f} hours old, marking as failed")
                            
                            self.jobs_table.update_item(
                                Key={"job_id": job_id},
                                UpdateExpression="SET #status = :status, updated_at = :updated, error_message = :error",
                                ExpressionAttributeNames={"#status": "status"},
                                ExpressionAttributeValues={
                                    ":status": "failed",
                                    ":updated": datetime.utcnow().isoformat(),
                                    ":error": "Job timed out - no results found after 1 hour"
                                }
                            )
                            
                            fixed_jobs.append({
                                "job_id": job_id,
                                "file_key": file_key,
                                "action": "marked_failed"
                            })
                    else:
                        print(f"❌ Error checking results for job {job_id}: {e}")
            
            print(f"🎉 Fixed {len(fixed_jobs)} jobs")
            return {"fixed_jobs": fixed_jobs, "total_fixed": len(fixed_jobs)}
            
        except Exception as e:
            print(f"❌ Error fixing pending jobs: {e}")
            return {"error": str(e)}
    
    def clear_dlq_messages(self) -> Dict:
        """Clear messages from dead letter queue"""
        print("🧹 Clearing Dead Letter Queue...")
        
        try:
            # Get all messages from DLQ
            messages_cleared = 0
            
            while True:
                response = self.sqs.receive_message(
                    QueueUrl=self.dlq_url,
                    MaxNumberOfMessages=10,
                    WaitTimeSeconds=1
                )
                
                messages = response.get("Messages", [])
                if not messages:
                    break
                
                # Delete messages
                for message in messages:
                    self.sqs.delete_message(
                        QueueUrl=self.dlq_url,
                        ReceiptHandle=message["ReceiptHandle"]
                    )
                    messages_cleared += 1
                    
                    # Log the message content for debugging
                    try:
                        body = json.loads(message["Body"])
                        print(f"   Cleared DLQ message for job: {body.get('job_id', 'unknown')}")
                    except:
                        print(f"   Cleared DLQ message: {message['Body'][:50]}...")
            
            print(f"✅ Cleared {messages_cleared} messages from DLQ")
            return {"messages_cleared": messages_cleared}
            
        except Exception as e:
            print(f"❌ Error clearing DLQ: {e}")
            return {"error": str(e)}
    
    def scale_up_ecs_service(self, desired_count: int = 1) -> Dict:
        """Manually scale up ECS service to process any remaining messages"""
        print(f"📈 Scaling ECS service to {desired_count} tasks...")
        
        try:
            response = self.ecs.update_service(
                cluster=self.cluster_name,
                service=self.service_name,
                desiredCount=desired_count
            )
            
            print(f"✅ ECS service scaled to {desired_count} tasks")
            return {
                "desired_count": desired_count,
                "service_arn": response["service"]["serviceArn"]
            }
            
        except Exception as e:
            print(f"❌ Error scaling ECS service: {e}")
            return {"error": str(e)}
    
    def test_job_submission(self, test_file_key: str = None) -> Dict:
        """Test end-to-end job submission flow"""
        print("🧪 Testing job submission flow...")
        
        if not test_file_key:
            # Use an existing file from S3
            s3 = self.session.client("s3")
            bucket_name = "biormikastack-s3storageedfstoragebucket8b55a415-vt2ocke56liv"
            
            try:
                response = s3.list_objects_v2(
                    Bucket=bucket_name,
                    Prefix="edf-files/",
                    MaxKeys=1
                )
                
                if "Contents" in response:
                    test_file_key = response["Contents"][0]["Key"]
                    print(f"📁 Using test file: {test_file_key}")
                else:
                    print("❌ No EDF files found in S3 for testing")
                    return {"error": "No test files available"}
                    
            except Exception as e:
                print(f"❌ Error finding test file: {e}")
                return {"error": str(e)}
        
        try:
            # Create a test job message
            import uuid
            test_job_id = str(uuid.uuid4())
            
            test_message = {
                "job_id": test_job_id,
                "file_key": test_file_key,
                "user_email": "<EMAIL>",
                "parameters": {
                    "frequency": {"low_cutoff": 80, "high_cutoff": 500},
                    "thresholds": {"amplitude1": 3, "amplitude2": 5}
                }
            }
            
            # Send message to SQS
            self.sqs.send_message(
                QueueUrl=self.queue_url,
                MessageBody=json.dumps(test_message)
            )
            
            print(f"✅ Test job submitted: {test_job_id}")
            print(f"📝 Monitor this job ID to verify end-to-end processing")
            
            return {
                "test_job_id": test_job_id,
                "file_key": test_file_key,
                "message": "Test job submitted successfully"
            }
            
        except Exception as e:
            print(f"❌ Error submitting test job: {e}")
            return {"error": str(e)}
    
    def run_complete_fix(self) -> Dict:
        """Run complete pipeline fix"""
        print("🚀 Running Complete Pipeline Fix...\n")
        
        results = {
            "timestamp": datetime.utcnow().isoformat(),
            "steps": {}
        }
        
        # Step 1: Fix pending jobs
        print("Step 1: Fix pending jobs")
        results["steps"]["fix_pending"] = self.fix_pending_jobs()
        print()
        
        # Step 2: Clear DLQ
        print("Step 2: Clear dead letter queue")
        results["steps"]["clear_dlq"] = self.clear_dlq_messages()
        print()
        
        # Step 3: Scale up ECS service
        print("Step 3: Scale up ECS service")
        results["steps"]["scale_ecs"] = self.scale_up_ecs_service(1)
        print()
        
        # Step 4: Test job submission
        print("Step 4: Test job submission")
        results["steps"]["test_submission"] = self.test_job_submission()
        print()
        
        print("="*60)
        print("🎉 PIPELINE FIX COMPLETE")
        print("="*60)
        
        # Summary
        fixed_jobs = results["steps"]["fix_pending"].get("total_fixed", 0)
        cleared_messages = results["steps"]["clear_dlq"].get("messages_cleared", 0)
        
        print(f"✅ Fixed {fixed_jobs} pending jobs")
        print(f"✅ Cleared {cleared_messages} DLQ messages")
        print(f"✅ ECS service scaled up")
        print(f"✅ Test job submitted")
        
        if "test_job_id" in results["steps"]["test_submission"]:
            test_job_id = results["steps"]["test_submission"]["test_job_id"]
            print(f"\n📝 Monitor test job: {test_job_id}")
            print("   Check DynamoDB and CloudWatch logs to verify processing")
        
        return results


def main():
    """Main entry point"""
    try:
        fixer = PipelineFixer()
        results = fixer.run_complete_fix()
        
        # Save results
        with open("pipeline_fix_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: pipeline_fix_results.json")
        
    except NoCredentialsError:
        print("❌ AWS credentials not configured. Please run 'aws configure --profile biormika'")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
